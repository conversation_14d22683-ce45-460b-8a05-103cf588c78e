# ConvertoKit

**Meta Ads & Web Analytics Specialist Website**

A modern, professional website built with <PERSON><PERSON> and <PERSON><PERSON> for Meta advertising, tracking implementation, and analytics consulting services.

## 🚀 Project Overview

convertokit is a comprehensive service-based website that showcases expertise in Meta advertising, web analytics, and conversion optimization. Built with cutting-edge technology, it provides a seamless experience for businesses seeking digital marketing consultation and services.

### Target Audience
- Businesses looking for Meta advertising services
- E-commerce owners needing tracking solutions
- Entrepreneurs seeking digital marketing consultation
- Companies requiring analytics and conversion optimization

## 🛠 Technology Stack

### Backend
- **Framework**: PHP Laravel 12.x with React Starter Kit
- **Database**: MySQL
- **Authentication**: <PERSON><PERSON>'s built-in authentication system
- **Real-time**: <PERSON><PERSON> Reverb for notifications and communication
- **Testing**: PHPUnit for backend testing

### Frontend
- **Framework**: React 19 with TypeScript
- **Routing**: Inertia.js for SPA-like experience
- **Styling**: Tailwind CSS 4.0
- **UI Components**: shadcn/ui
- **Testing**: Vitest for frontend testing
- **Code Quality**: ESLint for linting

### Payment Integration
- **Primary Gateway**: Paddle
- **Crypto Gateway**: CoinBase Commerce

### Analytics & Tracking
- **Facebook Pixel**: Advanced event tracking and conversion optimization
- **Google Analytics 4 (GA4)**: Comprehensive user behavior and conversion tracking
- **Google Tag Manager (GTM)**: Centralized tag management and custom event tracking
- **Server-Side Tracking**: Enhanced data accuracy with Conversion API integration

## 📋 Features

### Core Features
- **Content Management System**: WYSIWYG editor, media library, SEO optimization
- **User Authentication**: Role-based access control (Admin, Client, Team Member)
- **Consultation Booking**: Calendar integration with payment processing
- **Payment Integration**: Paddle and CoinBase Commerce support
- **Real-time Communication**: Laravel Reverb for notifications and live chat
- **Advanced Analytics**: Comprehensive tracking with Facebook Pixel, GA4, and GTM

### Advanced Features
- **Analytics Dashboard**: Real-time visitor data and conversion tracking
- **SEO Optimization**: Meta tags, sitemaps, schema markup
- **Email System**: Automated sequences and transactional emails
- **Privacy Compliance**: GDPR/CCPA compliant tracking and consent management

## 🏗 Website Structure

### Public Pages
- **Home** (`/`) - Hero section, services overview, team introduction
- **About** (`/about`) - Professional background and expertise
- **Services** (`/services`) - Meta Ads and Analytics service offerings
- **Team** (`/team`) - Team member profiles and expertise
- **Blog** (`/blog`) - Resources and industry insights
- **Contact** (`/contact`) - Contact form and consultation booking

### Protected Areas
- **Dashboard** (`/dashboard`) - User project overview and management
- **Admin Panel** (`/admin/*`) - Content management and analytics

## 📊 Analytics Implementation

### Facebook Pixel Integration
- Base pixel installation with GDPR compliance
- Standard events (PageView, ViewContent, Lead, Purchase)
- Custom events for consultation bookings and service interactions
- Conversion API for server-side tracking
- Enhanced matching for improved attribution

### Google Analytics 4 (GA4)
- Enhanced eCommerce tracking
- Custom event tracking for business goals
- User engagement metrics and conversion goals
- Custom dimensions and audience segments
- Attribution modeling and cross-platform correlation

### Google Tag Manager (GTM)
- Centralized tag management
- Custom data layer implementation
- Advanced trigger setup for user interactions
- Dynamic remarketing configuration

## 🗄 Database Schema

### Core Tables
- **Users**: Authentication and role management
- **Team Members**: Team profiles and expertise
- **Services**: Service offerings and pricing
- **Blog Posts**: Content management with SEO
- **Consultations**: Booking and scheduling system
- **Payments**: Transaction tracking and gateway integration
- **Analytics Events**: Comprehensive event tracking
- **Tracking Settings**: Analytics configuration management

## 🚀 Getting Started

### Prerequisites
- PHP 8.2+
- Node.js 18+
- MySQL 8.0+
- Composer
- npm/yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/convertokit.git
   cd convertokit
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Database setup**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

6. **Build assets**
   ```bash
   npm run build
   ```

### Development

1. **Start the development server**
   ```bash
   php artisan serve
   ```

2. **Start the asset watcher**
   ```bash
   npm run dev
   ```

3. **Start Laravel Reverb (for real-time features)**
   ```bash
   php artisan reverb:start
   ```

## 🧪 Testing

### Backend Testing
```bash
php artisan test
```

### Frontend Testing
```bash
npm run test
```

### Code Quality
```bash
npm run lint
```

## 📈 Analytics Configuration

### Environment Variables
```env
# Facebook Pixel
FACEBOOK_PIXEL_ID=your_pixel_id
FACEBOOK_ACCESS_TOKEN=your_access_token

# Google Analytics
GA4_MEASUREMENT_ID=your_ga4_id
GTM_CONTAINER_ID=your_gtm_id

# Conversion APIs
FACEBOOK_CONVERSION_API_TOKEN=your_conversion_api_token
GA4_API_SECRET=your_ga4_api_secret
```

### Tracking Setup
1. Configure Facebook Pixel ID in environment
2. Set up GA4 measurement ID
3. Configure GTM container
4. Enable Conversion API tokens
5. Set up server-side tracking endpoints

## 💳 Payment Gateway Setup

### Paddle Configuration
```env
PADDLE_VENDOR_ID=your_vendor_id
PADDLE_VENDOR_AUTH_CODE=your_auth_code
PADDLE_ENVIRONMENT=sandbox # or live
```

### CoinBase Commerce
```env
COINBASE_COMMERCE_API_KEY=your_api_key
COINBASE_COMMERCE_WEBHOOK_SECRET=your_webhook_secret
```

## 🔒 Security & Privacy

- HTTPS enforcement
- Input sanitization and XSS protection
- CSRF protection
- SQL injection prevention
- GDPR/CCPA compliance
- Consent-based tracking
- Data anonymization options

## 📱 Responsive Design

- Mobile-first approach
- Tablet optimization
- Desktop enhancement
- Touch-friendly interactions
- Cross-browser compatibility

## 🎨 UI/UX Design

- Professional blue/navy color scheme
- Modern typography with readable fonts
- shadcn/ui component library
- Clean, conversion-focused layouts
- Accessibility compliance (WCAG 2.1)

## 🚀 Deployment

### Production Requirements
- PHP 8.2+ with required extensions
- MySQL 8.0+ or compatible database
- Redis for caching and sessions
- SSL certificate
- CDN for asset delivery

### Performance Targets
- Page load time < 3 seconds
- Mobile performance score > 90
- SEO score > 95
- Accessibility compliance

## 📊 Success Metrics

### Technical Metrics
- Page load speed and uptime
- Error rates and test coverage
- Security scan results

### Business Metrics
- Consultation bookings
- Contact form submissions
- Newsletter sign-ups
- Conversion rates

### Analytics Metrics
- Facebook Pixel event match quality >8/10
- GA4 data collection accuracy >95%
- Cross-platform conversion alignment <10% variance

## 🔮 Future Enhancements

### Phase 2 Features
- Client portal with project tracking
- Resource library and downloads
- Webinar booking system
- Case study showcase
- Advanced CRM integration

### Advanced Analytics
- Machine learning integration for lead scoring
- Predictive analytics and customer lifetime value
- Advanced attribution modeling
- Cross-device journey mapping

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Yearon Suraiya** - Meta Ads & Analytics Specialist
- **Jahidul Islam** - Google Ads Expert
- **Rasel Sikder** - Google Ads and Web Analytics Expert
- **Amit Roy** - [Role to be defined]
- **Sohel Ahmed Sahil** - [Role to be defined]
- **Firoz Anam** - Full Stack Developer

## 📞 Support

For support and inquiries:
- Email: [<EMAIL>](mailto:<EMAIL>)
- Website: [https://convertokit.com](https://convertokit.com)

---

**Built with ❤️ using Laravel and React**