<?php

namespace Database\Factories;

use App\Models\Payment;
use App\Models\User;
use App\Models\Consultation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'consultation_id' => Consultation::factory(),
            'amount' => $this->faker->randomFloat(2, 50, 500),
            'currency' => 'USD',
            'payment_method' => $this->faker->randomElement(['paddle', 'coinbase']),
            'payment_gateway_id' => $this->faker->optional()->uuid(),
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'refunded']),
            'gateway_response' => $this->faker->optional()->randomElement([
                ['source' => 'web'],
                ['source' => 'mobile'],
                ['discount_applied' => true],
                null
            ]),
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => function (array $attributes) {
                return $this->faker->dateTimeBetween($attributes['created_at'], 'now');
            },
        ];
    }

    /**
     * Indicate that the payment is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'payment_gateway_id' => $this->faker->uuid(),
        ]);
    }

    /**
     * Indicate that the payment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'payment_gateway_id' => null,
        ]);
    }

    /**
     * Indicate that the payment failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'payment_gateway_id' => null,
        ]);
    }

    /**
     * Indicate that the payment was refunded.
     */
    public function refunded(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'refunded',
            'payment_gateway_id' => $this->faker->uuid(),
        ]);
    }

    /**
     * Indicate that the payment uses Paddle.
     */
    public function paddle(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'paddle',
            'payment_gateway_id' => 'txn_' . $this->faker->randomNumber(8),
        ]);
    }

    /**
     * Indicate that the payment uses Coinbase.
     */
    public function coinbase(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'coinbase',
            'payment_gateway_id' => 'CB-' . strtoupper($this->faker->bothify('???????')),
        ]);
    }

    /**
     * Create a payment with a specific amount.
     */
    public function amount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amount,
        ]);
    }

    /**
     * Create a payment for a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Create a payment for a specific consultation.
     */
    public function forConsultation(Consultation $consultation): static
    {
        return $this->state(fn (array $attributes) => [
            'consultation_id' => $consultation->id,
            'user_id' => $consultation->user_id,
        ]);
    }
}
