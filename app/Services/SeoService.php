<?php

namespace App\Services;

use App\Models\Service;
use App\Models\BlogPost;
use App\Models\TeamMember;

class SeoService
{
    /**
     * Generate structured data for home page
     */
    public function getHomeStructuredData(array $services = [], array $teamMembers = []): array
    {
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'ConvertOKit',
            'description' => 'Expert Meta advertising management and web analytics implementation to maximize your ROI',
            'url' => url('/'),
            'logo' => asset('images/logo.png'),
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => '******-0123',
                'contactType' => 'customer service',
                'availableLanguage' => 'English'
            ],
            'address' => [
                '@type' => 'PostalAddress',
                'addressCountry' => 'US'
            ],
            'sameAs' => [
                'https://facebook.com/convertokit',
                'https://twitter.com/convertokit',
                'https://linkedin.com/company/convertokit'
            ]
        ];

        // Add services if available
        if (!empty($services)) {
            $structuredData['hasOfferCatalog'] = [
                '@type' => 'OfferCatalog',
                'name' => 'Digital Marketing Services',
                'itemListElement' => array_map(function ($service) {
                    return [
                        '@type' => 'Offer',
                        'itemOffered' => [
                            '@type' => 'Service',
                            'name' => $service['title'],
                            'description' => $service['description'],
                            'category' => $service['category']
                        ]
                    ];
                }, $services)
            ];
        }

        return $structuredData;
    }

    /**
     * Generate structured data for service page
     */
    public function getServiceStructuredData(Service $service): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Service',
            'name' => $service->title,
            'description' => $service->description,
            'provider' => [
                '@type' => 'Organization',
                'name' => 'ConvertOKit',
                'url' => url('/')
            ],
            'category' => $service->category,
            'offers' => [
                '@type' => 'Offer',
                'description' => $service->description,
                'priceRange' => $service->price_range
            ],
            'url' => route('service.show', $service->slug)
        ];
    }

    /**
     * Generate structured data for blog post
     */
    public function getBlogPostStructuredData(BlogPost $post): array
    {
        $structuredData = [
            '@context' => 'https://schema.org',
            '@type' => 'BlogPosting',
            'headline' => $post->title,
            'description' => $post->excerpt,
            'articleBody' => strip_tags($post->content),
            'url' => route('blog.show', $post->slug),
            'datePublished' => $post->published_at?->toISOString(),
            'dateModified' => $post->updated_at->toISOString(),
            'publisher' => [
                '@type' => 'Organization',
                'name' => 'ConvertOKit',
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => asset('images/logo.png')
                ]
            ]
        ];

        // Add author if available
        if ($post->author) {
            $structuredData['author'] = [
                '@type' => 'Person',
                'name' => $post->author->name
            ];
        }

        // Add featured image if available
        if ($post->featured_image) {
            $structuredData['image'] = [
                '@type' => 'ImageObject',
                'url' => asset('storage/' . $post->featured_image),
                'caption' => $post->title
            ];
        }

        // Add category if available
        if ($post->category) {
            $structuredData['articleSection'] = $post->category->name;
        }

        return $structuredData;
    }

    /**
     * Generate structured data for team page
     */
    public function getTeamStructuredData(array $teamMembers): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => 'ConvertOKit',
            'url' => url('/'),
            'employee' => array_map(function ($member) {
                return [
                    '@type' => 'Person',
                    'name' => $member['name'],
                    'jobTitle' => $member['position'],
                    'description' => $member['bio'] ?? '',
                    'worksFor' => [
                        '@type' => 'Organization',
                        'name' => 'ConvertOKit'
                    ]
                ];
            }, $teamMembers)
        ];
    }

    /**
     * Generate breadcrumb structured data
     */
    public function getBreadcrumbStructuredData(array $breadcrumbs): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => array_map(function ($breadcrumb, $index) {
                return [
                    '@type' => 'ListItem',
                    'position' => $index + 1,
                    'name' => $breadcrumb['name'],
                    'item' => $breadcrumb['url']
                ];
            }, $breadcrumbs, array_keys($breadcrumbs))
        ];
    }

    /**
     * Generate FAQ structured data
     */
    public function getFaqStructuredData(array $faqs): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => array_map(function ($faq) {
                return [
                    '@type' => 'Question',
                    'name' => $faq['question'],
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => $faq['answer']
                    ]
                ];
            }, $faqs)
        ];
    }

    /**
     * Generate meta tags array for a page
     */
    public function generateMetaTags(array $data): array
    {
        $meta = [
            'title' => $data['title'] ?? 'ConvertOKit',
            'description' => $data['description'] ?? 'Expert Meta advertising and web analytics services',
            'keywords' => $data['keywords'] ?? 'Meta ads, Facebook advertising, web analytics',
            'canonical' => $data['canonical'] ?? url('/'),
            'og_title' => $data['og_title'] ?? $data['title'] ?? 'ConvertOKit',
            'og_description' => $data['og_description'] ?? $data['description'] ?? 'Expert Meta advertising and web analytics services',
            'og_type' => $data['og_type'] ?? 'website',
            'og_url' => $data['og_url'] ?? $data['canonical'] ?? url('/'),
            'og_site_name' => 'ConvertOKit',
            'twitter_card' => 'summary_large_image',
            'twitter_title' => $data['twitter_title'] ?? $data['title'] ?? 'ConvertOKit',
            'twitter_description' => $data['twitter_description'] ?? $data['description'] ?? 'Expert Meta advertising and web analytics services'
        ];

        // Add optional meta tags
        if (isset($data['og_image'])) {
            $meta['og_image'] = $data['og_image'];
            $meta['twitter_image'] = $data['og_image'];
        }

        if (isset($data['published_time'])) {
            $meta['article_published_time'] = $data['published_time'];
        }

        if (isset($data['modified_time'])) {
            $meta['article_modified_time'] = $data['modified_time'];
        }

        if (isset($data['author'])) {
            $meta['article_author'] = $data['author'];
        }

        return $meta;
    }

    /**
     * Generate robots meta tag
     */
    public function getRobotsTag(bool $index = true, bool $follow = true): string
    {
        $robots = [];
        $robots[] = $index ? 'index' : 'noindex';
        $robots[] = $follow ? 'follow' : 'nofollow';
        
        return implode(', ', $robots);
    }
}
