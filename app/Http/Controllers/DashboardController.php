<?php

namespace App\Http\Controllers;

use App\Models\Consultation;
use App\Models\Payment;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the user dashboard
     */
    public function index(Request $request): Response|RedirectResponse
    {
        $user = Auth::user();

        // Redirect admin users to admin dashboard
        if ($user->role === 'admin') {
            return redirect()->route('admin.dashboard');
        }

        // Get user-specific data based on role
        $dashboardData = $this->getDashboardData($user);

        return Inertia::render('dashboard', $dashboardData);
    }

    /**
     * Get dashboard data based on user role
     */
    private function getDashboardData(User $user): array
    {
        $baseData = [
            'user' => $user->load(['teamMember']),
        ];

        switch ($user->role) {
            case 'admin':
                return array_merge($baseData, $this->getAdminDashboardData());
            case 'team_member':
                return array_merge($baseData, $this->getTeamMemberDashboardData($user));
            case 'client':
            default:
                return array_merge($baseData, $this->getClientDashboardData($user));
        }
    }

    /**
     * Get admin dashboard data
     */
    private function getAdminDashboardData(): array
    {
        $totalUsers = User::count();
        $totalClients = User::where('role', 'client')->count();
        $totalTeamMembers = User::where('role', 'team_member')->count();
        
        $totalConsultations = Consultation::count();
        $pendingConsultations = Consultation::where('status', 'pending')->count();
        $completedConsultations = Consultation::where('status', 'completed')->count();
        
        $totalRevenue = Payment::where('status', 'completed')->sum('amount');
        $monthlyRevenue = Payment::where('status', 'completed')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');
        
        $recentConsultations = Consultation::with(['user', 'service'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        
        $recentPayments = Payment::with(['user', 'consultation.service'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return [
            'role' => 'admin',
            'stats' => [
                'total_users' => $totalUsers,
                'total_clients' => $totalClients,
                'total_team_members' => $totalTeamMembers,
                'total_consultations' => $totalConsultations,
                'pending_consultations' => $pendingConsultations,
                'completed_consultations' => $completedConsultations,
                'total_revenue' => $totalRevenue,
                'monthly_revenue' => $monthlyRevenue,
            ],
            'recent_consultations' => $recentConsultations,
            'recent_payments' => $recentPayments,
        ];
    }

    /**
     * Get team member dashboard data
     */
    private function getTeamMemberDashboardData(User $user): array
    {
        // Team members can see consultations they're involved in
        $assignedConsultations = Consultation::with(['user', 'service'])
            ->where('status', '!=', 'cancelled')
            ->orderBy('consultation_date', 'asc')
            ->limit(10)
            ->get();

        $completedThisMonth = Consultation::where('status', 'completed')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        return [
            'role' => 'team_member',
            'stats' => [
                'assigned_consultations' => $assignedConsultations->count(),
                'completed_this_month' => $completedThisMonth,
            ],
            'assigned_consultations' => $assignedConsultations,
        ];
    }

    /**
     * Get client dashboard data
     */
    private function getClientDashboardData(User $user): array
    {
        $consultations = Consultation::with(['service', 'payment'])
            ->where('user_id', $user->id)
            ->orderBy('consultation_date', 'desc')
            ->get();

        $payments = Payment::with(['consultation.service'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        $upcomingConsultations = $consultations
            ->where('status', 'confirmed')
            ->where('consultation_date', '>', now())
            ->take(3);

        $recentConsultations = $consultations
            ->where('status', 'completed')
            ->take(5);

        $totalSpent = $payments->where('status', 'completed')->sum('amount');
        $pendingPayments = $payments->where('status', 'pending')->count();

        return [
            'role' => 'client',
            'stats' => [
                'total_consultations' => $consultations->count(),
                'upcoming_consultations' => $upcomingConsultations->count(),
                'completed_consultations' => $consultations->where('status', 'completed')->count(),
                'total_spent' => $totalSpent,
                'pending_payments' => $pendingPayments,
            ],
            'upcoming_consultations' => $upcomingConsultations->values(),
            'recent_consultations' => $recentConsultations->values(),
            'recent_payments' => $payments->take(5)->values(),
        ];
    }
}
