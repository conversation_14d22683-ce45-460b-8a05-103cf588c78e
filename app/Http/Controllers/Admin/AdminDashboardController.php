<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Service;
use App\Models\BlogPost;
use App\Models\Category;
use App\Models\TeamMember;
use App\Models\Consultation;
use App\Models\Payment;
use App\Models\ContactSubmission;
use App\Models\NewsletterSubscription;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AdminDashboardController extends Controller
{
    public function __construct(
        private AnalyticsService $analyticsService
    ) {}

    /**
     * Admin dashboard overview
     */
    public function index(): Response
    {
        $stats = [
            'total_users' => User::count(),
            'total_clients' => User::where('role', 'client')->count(),
            'total_team_members' => User::where('role', 'team_member')->count(),
            'total_services' => Service::where('is_active', true)->count(),
            'total_blog_posts' => BlogPost::where('status', 'published')->count(),
            'total_consultations' => Consultation::count(),
            'pending_consultations' => Consultation::where('status', 'pending')->count(),
            'completed_consultations' => Consultation::where('status', 'completed')->count(),
            'total_revenue' => Payment::where('status', 'completed')->sum('amount'),
            'monthly_revenue' => Payment::where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
            'pending_contacts' => ContactSubmission::where('status', 'new')->count(),
            'newsletter_subscribers' => NewsletterSubscription::where('status', 'active')->count(),
        ];

        $recentActivity = [
            'recent_users' => User::latest()->limit(5)->get(),
            'recent_consultations' => Consultation::with(['user', 'service'])->latest()->limit(5)->get(),
            'recent_payments' => Payment::with(['user', 'consultation.service'])->latest()->limit(5)->get(),
            'recent_contacts' => ContactSubmission::latest()->limit(5)->get(),
        ];

        return Inertia::render('admin/dashboard', [
            'stats' => $stats,
            'recentActivity' => $recentActivity,
        ]);
    }

    /**
     * User management page
     */
    public function users(Request $request): Response
    {
        $query = User::query();

        if ($request->has('role')) {
            $query->where('role', $request->get('role'));
        }

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%")
                  ->orWhere('company', 'LIKE', "%{$search}%");
            });
        }

        $users = $query->with(['teamMember'])
            ->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('admin/users/index', [
            'users' => $users,
            'filters' => $request->only(['role', 'search']),
        ]);
    }

    /**
     * User detail page
     */
    public function userShow(User $user): Response
    {
        $user->load(['teamMember', 'consultations.service', 'payments.consultation']);

        return Inertia::render('admin/users/show', [
            'user' => $user,
        ]);
    }

    /**
     * Service management page
     */
    public function services(Request $request): Response
    {
        $query = Service::query();

        if ($request->has('category')) {
            $query->where('category', $request->get('category'));
        }

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%");
            });
        }

        $services = $query->orderBy('sort_order')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('admin/services/index', [
            'services' => $services,
            'filters' => $request->only(['category', 'search']),
        ]);
    }

    /**
     * Service detail page
     */
    public function serviceShow(Service $service): Response
    {
        $service->load(['consultations.user']);

        return Inertia::render('admin/services/show', [
            'service' => $service,
        ]);
    }

    /**
     * Blog management overview
     */
    public function blog(): Response
    {
        $stats = [
            'total_posts' => BlogPost::count(),
            'published_posts' => BlogPost::where('status', 'published')->count(),
            'draft_posts' => BlogPost::where('status', 'draft')->count(),
            'total_categories' => Category::count(),
        ];

        $recentPosts = BlogPost::with(['category', 'author'])
            ->latest()
            ->limit(10)
            ->get();

        return Inertia::render('admin/blog/index', [
            'stats' => $stats,
            'recentPosts' => $recentPosts,
        ]);
    }

    /**
     * Blog posts management
     */
    public function blogPosts(Request $request): Response
    {
        $query = BlogPost::with(['category', 'author']);

        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->has('category')) {
            $query->where('category_id', $request->get('category'));
        }

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'LIKE', "%{$search}%")
                  ->orWhere('excerpt', 'LIKE', "%{$search}%");
            });
        }

        $posts = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        $categories = Category::orderBy('name')->get();

        return Inertia::render('admin/blog/posts', [
            'posts' => $posts,
            'categories' => $categories,
            'filters' => $request->only(['status', 'category', 'search']),
        ]);
    }

    /**
     * Blog post detail page
     */
    public function blogPostShow(BlogPost $post): Response
    {
        $post->load(['category', 'author', 'comments.user']);

        return Inertia::render('admin/blog/posts/show', [
            'post' => $post,
        ]);
    }

    /**
     * Blog categories management
     */
    public function blogCategories(): Response
    {
        $categories = Category::withCount('blogPosts')
            ->orderBy('sort_order')
            ->get();

        return Inertia::render('admin/blog/categories', [
            'categories' => $categories,
        ]);
    }

    /**
     * Team management page
     */
    public function team(): Response
    {
        $teamMembers = TeamMember::with(['user'])
            ->orderBy('sort_order')
            ->get();

        return Inertia::render('admin/team/index', [
            'teamMembers' => $teamMembers,
        ]);
    }

    /**
     * Team member detail page
     */
    public function teamShow(TeamMember $member): Response
    {
        $member->load(['user']);

        return Inertia::render('admin/team/show', [
            'member' => $member,
        ]);
    }

    /**
     * Consultation management page
     */
    public function consultations(Request $request): Response
    {
        $query = Consultation::with(['user', 'service', 'payment']);

        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->has('payment_status')) {
            $query->where('payment_status', $request->get('payment_status'));
        }

        if ($request->has('search')) {
            $search = $request->get('search');
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        $consultations = $query->orderBy('consultation_date', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('admin/consultations/index', [
            'consultations' => $consultations,
            'filters' => $request->only(['status', 'payment_status', 'search']),
        ]);
    }

    /**
     * Consultation detail page
     */
    public function consultationShow(Consultation $consultation): Response
    {
        $consultation->load(['user', 'service', 'payment']);

        return Inertia::render('admin/consultations/show', [
            'consultation' => $consultation,
        ]);
    }

    /**
     * Payment management page
     */
    public function payments(Request $request): Response
    {
        $query = Payment::with(['user', 'consultation.service']);

        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->has('payment_method')) {
            $query->where('payment_method', $request->get('payment_method'));
        }

        $payments = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('admin/payments/index', [
            'payments' => $payments,
            'filters' => $request->only(['status', 'payment_method']),
        ]);
    }

    /**
     * Payment detail page
     */
    public function paymentShow(Payment $payment): Response
    {
        $payment->load(['user', 'consultation.service']);

        return Inertia::render('admin/payments/show', [
            'payment' => $payment,
        ]);
    }

    /**
     * Analytics page
     */
    public function analytics(Request $request): Response
    {
        $filters = [
            'date_from' => $request->get('date_from', now()->subDays(30)->format('Y-m-d')),
            'date_to' => $request->get('date_to', now()->format('Y-m-d')),
        ];

        $analyticsData = $this->analyticsService->getDashboardData($filters);

        return Inertia::render('admin/analytics', [
            'analyticsData' => $analyticsData,
            'filters' => $filters,
        ]);
    }

    /**
     * Contact submissions page
     */
    public function contacts(Request $request): Response
    {
        $query = ContactSubmission::query();

        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        $contacts = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('admin/contacts', [
            'contacts' => $contacts,
            'filters' => $request->only(['status']),
        ]);
    }

    /**
     * Newsletter management page
     */
    public function newsletter(): Response
    {
        $subscribers = NewsletterSubscription::orderBy('created_at', 'desc')
            ->paginate(20);

        $stats = [
            'total_subscribers' => NewsletterSubscription::count(),
            'active_subscribers' => NewsletterSubscription::where('status', 'active')->count(),
            'unsubscribed' => NewsletterSubscription::where('status', 'unsubscribed')->count(),
        ];

        return Inertia::render('admin/newsletter', [
            'subscribers' => $subscribers,
            'stats' => $stats,
        ]);
    }

    /**
     * Admin settings page
     */
    public function settings(): Response
    {
        return Inertia::render('admin/settings');
    }
}
