<?php

namespace App\Http\Controllers;

use App\Services\ServiceService;
use App\Services\TeamService;
use App\Services\BlogService;
use App\Services\SeoService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PublicController extends Controller
{
    public function __construct(
        private ServiceService $serviceService,
        private TeamService $teamService,
        private BlogService $blogService,
        private SeoService $seoService
    ) {}

    /**
     * Show the home page
     */
    public function home(): Response
    {
        try {
            // Get featured services (first 3)
            $services = $this->serviceService->getActiveServices([
                'paginate' => false,
                'limit' => 3,
                'sort_by' => 'sort_order',
                'sort_direction' => 'asc'
            ]);

            // Get featured team members (first 4)
            $teamMembers = $this->teamService->getActiveMembers([
                'paginate' => false,
                'limit' => 4,
                'sort_by' => 'sort_order',
                'sort_direction' => 'asc'
            ]);

            // Get latest blog posts (first 3)
            $blogPosts = $this->blogService->getPublishedPosts([
                'paginate' => false,
                'limit' => 3,
                'sort_by' => 'published_at',
                'sort_direction' => 'desc'
            ]);

            // Generate SEO data
            $seoData = $this->seoService->generateMetaTags([
                'title' => 'ConvertOKit - Meta Ads & Web Analytics Specialists',
                'description' => 'Expert Meta advertising management and web analytics implementation to maximize your ROI. Professional Facebook & Instagram ads, GA4 setup, and conversion tracking.',
                'keywords' => 'Meta ads, Facebook advertising, Instagram ads, Google Analytics, web analytics, conversion tracking, digital marketing',
                'og_image' => asset('images/og-home.jpg'),
                'canonical' => route('home')
            ]);

            // Generate structured data
            $structuredData = $this->seoService->getHomeStructuredData(
                $services->toArray(),
                $teamMembers->toArray()
            );

            return Inertia::render('public/home', [
                'services' => $services,
                'teamMembers' => $teamMembers,
                'blogPosts' => $blogPosts,
                'meta' => $seoData,
                'structuredData' => $structuredData
            ]);
        } catch (\Exception $e) {
            // Log error and return with empty data
            \Log::error('Error loading home page: ' . $e->getMessage());
            
            return Inertia::render('public/home', [
                'services' => [],
                'teamMembers' => [],
                'blogPosts' => [],
                'meta' => [
                    'title' => 'ConvertOKit - Meta Ads & Web Analytics Specialists',
                    'description' => 'Expert Meta advertising management and web analytics implementation to maximize your ROI.',
                    'canonical' => route('home')
                ]
            ]);
        }
    }

    /**
     * Show the about page
     */
    public function about(): Response
    {
        return Inertia::render('public/about', [
            'meta' => [
                'title' => 'About Us - ConvertOKit',
                'description' => 'Learn about our team of Meta advertising and web analytics experts. Discover our mission, values, and commitment to maximizing your digital marketing ROI.',
                'keywords' => 'about ConvertOKit, Meta ads experts, digital marketing team, web analytics specialists',
                'canonical' => route('about')
            ]
        ]);
    }

    /**
     * Show the services page
     */
    public function services(Request $request): Response
    {
        try {
            $filters = [
                'search' => $request->get('search'),
                'category' => $request->get('category'),
                'sort_by' => $request->get('sort_by', 'sort_order'),
                'sort_direction' => $request->get('sort_direction', 'asc'),
                'paginate' => false
            ];

            $services = $this->serviceService->getActiveServices($filters);

            // Get unique categories for filtering
            $categories = $services->pluck('category')->unique()->values();

            return Inertia::render('public/services', [
                'services' => $services,
                'categories' => $categories,
                'filters' => $filters,
                'meta' => [
                    'title' => 'Our Services - ConvertOKit',
                    'description' => 'Comprehensive Meta advertising and web analytics services. From Facebook ads management to GA4 setup and conversion tracking.',
                    'keywords' => 'Meta ads services, Facebook advertising, Instagram ads, Google Analytics setup, conversion tracking, digital marketing services',
                    'canonical' => route('services')
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error loading services page: ' . $e->getMessage());
            
            return Inertia::render('public/services', [
                'services' => [],
                'categories' => [],
                'filters' => [],
                'meta' => [
                    'title' => 'Our Services - ConvertOKit',
                    'description' => 'Comprehensive Meta advertising and web analytics services.',
                    'canonical' => route('services')
                ]
            ]);
        }
    }

    /**
     * Show individual service detail page
     */
    public function serviceDetail(string $slug): Response
    {
        try {
            $service = $this->serviceService->findBySlug($slug);

            if (!$service) {
                abort(404, 'Service not found');
            }

            // Get related services (same category, excluding current)
            $relatedServices = $this->serviceService->getActiveServices([
                'category' => $service->category,
                'paginate' => false,
                'limit' => 3
            ])->filter(function ($relatedService) use ($service) {
                return $relatedService->id !== $service->id;
            })->take(3);

            // Generate SEO data
            $seoData = $this->seoService->generateMetaTags([
                'title' => $service->meta_title ?: $service->title . ' - ConvertOKit',
                'description' => $service->meta_description ?: $service->description,
                'keywords' => $service->category . ', ' . $service->title . ', digital marketing, Meta ads',
                'canonical' => route('service.show', $service->slug),
                'og_type' => 'article'
            ]);

            // Generate structured data
            $structuredData = $this->seoService->getServiceStructuredData($service);

            // Generate breadcrumb data
            $breadcrumbs = [
                ['name' => 'Home', 'url' => route('home')],
                ['name' => 'Services', 'url' => route('services')],
                ['name' => $service->title, 'url' => route('service.show', $service->slug)]
            ];
            $breadcrumbData = $this->seoService->getBreadcrumbStructuredData($breadcrumbs);

            return Inertia::render('public/service-detail', [
                'service' => $service,
                'relatedServices' => $relatedServices->values(),
                'meta' => $seoData,
                'structuredData' => [$structuredData, $breadcrumbData],
                'breadcrumbs' => $breadcrumbs
            ]);
        } catch (\Exception $e) {
            \Log::error('Error loading service detail page: ' . $e->getMessage());
            abort(404, 'Service not found');
        }
    }

    /**
     * Show the team page
     */
    public function team(): Response
    {
        try {
            $teamMembers = $this->teamService->getActiveMembers([
                'paginate' => false,
                'sort_by' => 'sort_order',
                'sort_direction' => 'asc'
            ]);

            return Inertia::render('public/team', [
                'teamMembers' => $teamMembers,
                'meta' => [
                    'title' => 'Our Team - ConvertOKit',
                    'description' => 'Meet our team of certified Meta advertising and web analytics experts. Learn about their expertise and experience.',
                    'keywords' => 'ConvertOKit team, Meta ads experts, digital marketing specialists, web analytics professionals',
                    'canonical' => route('team')
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error loading team page: ' . $e->getMessage());
            
            return Inertia::render('public/team', [
                'teamMembers' => [],
                'meta' => [
                    'title' => 'Our Team - ConvertOKit',
                    'description' => 'Meet our team of certified Meta advertising and web analytics experts.',
                    'canonical' => route('team')
                ]
            ]);
        }
    }

    /**
     * Show the blog page
     */
    public function blog(Request $request): Response
    {
        try {
            $filters = [
                'search' => $request->get('search'),
                'category_id' => $request->get('category_id'),
                'sort_by' => $request->get('sort_by', 'published_at'),
                'sort_direction' => $request->get('sort_direction', 'desc'),
                'paginate' => true,
                'limit' => 12
            ];

            $blogPosts = $this->blogService->getPublishedPosts($filters);
            $categories = $this->blogService->getActiveCategories();

            return Inertia::render('public/blog', [
                'blogPosts' => $blogPosts,
                'categories' => $categories,
                'filters' => $filters,
                'meta' => [
                    'title' => 'Blog - ConvertOKit',
                    'description' => 'Latest insights, tips, and strategies for Meta advertising, web analytics, and digital marketing optimization.',
                    'keywords' => 'digital marketing blog, Meta ads tips, Facebook advertising insights, web analytics guides',
                    'canonical' => route('blog')
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error loading blog page: ' . $e->getMessage());

            return Inertia::render('public/blog', [
                'blogPosts' => [],
                'categories' => [],
                'filters' => [],
                'meta' => [
                    'title' => 'Blog - ConvertOKit',
                    'description' => 'Latest insights, tips, and strategies for Meta advertising and web analytics.',
                    'canonical' => route('blog')
                ]
            ]);
        }
    }

    /**
     * Show individual blog post page
     */
    public function blogPost(string $slug): Response
    {
        try {
            $post = $this->blogService->findBySlug($slug);

            if (!$post) {
                abort(404, 'Blog post not found');
            }

            // Increment view count
            $this->blogService->incrementViews($post->id);

            // Get related posts (same category, excluding current)
            $relatedPosts = $this->blogService->getPublishedPosts([
                'category_id' => $post->category_id,
                'paginate' => false,
                'limit' => 4
            ])->filter(function ($relatedPost) use ($post) {
                return $relatedPost->id !== $post->id;
            })->take(3);

            // Generate SEO data
            $seoData = $this->seoService->generateMetaTags([
                'title' => $post->meta_title ?: $post->title . ' - ConvertOKit Blog',
                'description' => $post->meta_description ?: $post->excerpt,
                'keywords' => ($post->category->name ?? 'Digital Marketing') . ', ' . $post->title . ', digital marketing',
                'canonical' => route('blog.show', $post->slug),
                'og_type' => 'article',
                'og_image' => $post->featured_image ? asset('storage/' . $post->featured_image) : null,
                'published_time' => $post->published_at?->toISOString(),
                'modified_time' => $post->updated_at->toISOString(),
                'author' => $post->author->name ?? 'ConvertOKit Team'
            ]);

            // Generate structured data
            $structuredData = $this->seoService->getBlogPostStructuredData($post);

            // Generate breadcrumb data
            $breadcrumbs = [
                ['name' => 'Home', 'url' => route('home')],
                ['name' => 'Blog', 'url' => route('blog')],
                ['name' => $post->title, 'url' => route('blog.show', $post->slug)]
            ];
            $breadcrumbData = $this->seoService->getBreadcrumbStructuredData($breadcrumbs);

            return Inertia::render('public/blog-post', [
                'post' => $post,
                'relatedPosts' => $relatedPosts->values(),
                'meta' => $seoData,
                'structuredData' => [$structuredData, $breadcrumbData],
                'breadcrumbs' => $breadcrumbs
            ]);
        } catch (\Exception $e) {
            \Log::error('Error loading blog post page: ' . $e->getMessage());
            abort(404, 'Blog post not found');
        }
    }

    /**
     * Show the contact page
     */
    public function contact(): Response
    {
        return Inertia::render('public/contact', [
            'meta' => [
                'title' => 'Contact Us - ConvertOKit',
                'description' => 'Get in touch with our Meta advertising and web analytics experts. Book a free consultation or send us a message.',
                'keywords' => 'contact ConvertOKit, Meta ads consultation, digital marketing contact, web analytics help',
                'canonical' => route('contact')
            ]
        ]);
    }

    /**
     * Show the consultation booking page
     */
    public function bookConsultation(): Response
    {
        try {
            // Get available services for consultation booking
            $services = $this->serviceService->getActiveServices([
                'paginate' => false,
                'sort_by' => 'sort_order',
                'sort_direction' => 'asc'
            ]);

            return Inertia::render('public/book-consultation', [
                'services' => $services,
                'meta' => [
                    'title' => 'Book Consultation - ConvertOKit',
                    'description' => 'Schedule a free consultation with our Meta advertising and web analytics experts. Discuss your goals and get a customized strategy.',
                    'keywords' => 'book consultation, Meta ads consultation, digital marketing consultation, free consultation',
                    'canonical' => route('consultation.book')
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Error loading consultation booking page: ' . $e->getMessage());
            
            return Inertia::render('public/book-consultation', [
                'services' => [],
                'meta' => [
                    'title' => 'Book Consultation - ConvertOKit',
                    'description' => 'Schedule a free consultation with our Meta advertising and web analytics experts.',
                    'canonical' => route('consultation.book')
                ]
            ]);
        }
    }
}
