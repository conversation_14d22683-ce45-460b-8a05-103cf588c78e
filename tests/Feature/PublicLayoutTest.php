<?php

namespace Tests\Feature;

use App\Models\BlogPost;
use App\Models\Category;
use App\Models\Service;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PublicLayoutTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create services with the expected slugs
        $services = [
            ['title' => 'Meta Ads Management', 'slug' => 'meta-ads-management'],
            ['title' => 'Facebook Pixel Setup', 'slug' => 'facebook-pixel-setup'],
            ['title' => 'Google Analytics Setup', 'slug' => 'google-analytics-setup'],
            ['title' => 'Conversion Tracking', 'slug' => 'conversion-tracking'],
            ['title' => 'Special Service & More', 'slug' => 'special-service-more'],
            ['title' => 'Service with Symbols!', 'slug' => 'service-with-symbols'],
            ['title' => 'Very Long Service Name That Might Cause Issues', 'slug' => 'this-is-a-very-long-slug-that-might-cause-issues-if-not-handled-properly-in-the-routing-system'],
            // Add special character slugs
            ['title' => 'Meta Ads 2024', 'slug' => 'meta-ads-2024'],
            ['title' => 'Facebook Pixel v2', 'slug' => 'facebook-pixel-v2'],
            ['title' => 'Google Analytics 4', 'slug' => 'google-analytics-4'],
        ];

        foreach ($services as $serviceData) {
            Service::factory()->create([
                'title' => $serviceData['title'],
                'slug' => $serviceData['slug'],
                'category' => 'Test Category',
                'is_active' => true,
                'sort_order' => 1
            ]);
        }

        // Create categories and blog posts
        $category = Category::factory()->create([
            'name' => 'Meta Advertising',
            'slug' => 'meta-advertising',
            'is_active' => true
        ]);

        $user = User::factory()->create();

        $blogPosts = [
            ['title' => 'Meta Ads Best Practices', 'slug' => 'meta-ads-best-practices'],
            ['title' => 'Facebook Pixel Guide', 'slug' => 'facebook-pixel-guide'],
            ['title' => 'Google Analytics Setup', 'slug' => 'google-analytics-setup'],
            ['title' => 'Conversion Optimization Tips', 'slug' => 'conversion-optimization-tips'],
            ['title' => 'Special Post & More', 'slug' => 'special-post-more'],
            ['title' => 'Post with Symbols!', 'slug' => 'post-with-symbols'],
            ['title' => 'Very Long Post Title', 'slug' => 'this-is-a-very-long-slug-that-might-cause-issues-if-not-handled-properly-in-the-routing-system'],
            // Add special character slugs
            ['title' => 'Meta Ads 2024', 'slug' => 'meta-ads-2024'],
            ['title' => 'Facebook Pixel v2', 'slug' => 'facebook-pixel-v2'],
            ['title' => 'Google Analytics 4', 'slug' => 'google-analytics-4'],
        ];

        foreach ($blogPosts as $postData) {
            BlogPost::factory()->create([
                'title' => $postData['title'],
                'slug' => $postData['slug'],
                'excerpt' => 'Test excerpt',
                'content' => 'Test content',
                'status' => 'published',
                'published_at' => now(),
                'category_id' => $category->id,
                'author_id' => $user->id
            ]);
        }
    }

    /**
     * Test that public pages use the correct layout structure.
     */
    public function test_public_pages_use_correct_layout(): void
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        
        // Test that the response contains the expected Inertia component
        $response->assertInertia(fn ($page) => $page
            ->component('public/home')
        );
    }

    /**
     * Test that public pages are accessible to guests.
     */
    public function test_public_pages_accessible_to_guests(): void
    {
        $publicPages = [
            '/',
            '/about',
            '/services',
            '/team',
            '/blog',
            '/contact',
            '/book-consultation',
        ];

        foreach ($publicPages as $page) {
            $response = $this->get($page);
            $response->assertStatus(200);

            // Ensure it's not a redirect to login page
            $this->assertStringNotContainsString('/login', $response->headers->get('location', ''));
        }
    }

    /**
     * Test that public pages work with authenticated users.
     */
    public function test_public_pages_work_with_authenticated_users(): void
    {
        $user = \App\Models\User::factory()->create();
        
        $this->actingAs($user);

        $publicPages = [
            '/',
            '/about',
            '/services',
            '/team',
            '/blog',
            '/contact',
            '/book-consultation',
        ];

        foreach ($publicPages as $page) {
            $response = $this->get($page);
            $response->assertStatus(200);
        }
    }

    /**
     * Test that the home page redirects correctly.
     */
    public function test_home_page_routing(): void
    {
        $response = $this->get('/');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/home')
        );
    }

    /**
     * Test that service detail pages handle dynamic routing.
     */
    public function test_service_detail_dynamic_routing(): void
    {
        $testSlugs = [
            'meta-ads-management',
            'facebook-pixel-setup',
            'google-analytics-setup',
            'conversion-tracking',
        ];

        foreach ($testSlugs as $slug) {
            $response = $this->get("/services/{$slug}");
            
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page
                ->component('public/service-detail')
                ->has('service')
                ->where('service.slug', $slug)
            );
        }
    }

    /**
     * Test that blog post pages handle dynamic routing.
     */
    public function test_blog_post_dynamic_routing(): void
    {
        $testSlugs = [
            'meta-ads-best-practices',
            'facebook-pixel-guide',
            'google-analytics-setup',
            'conversion-optimization-tips',
        ];

        foreach ($testSlugs as $slug) {
            $response = $this->get("/blog/{$slug}");
            
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page
                ->component('public/blog-post')
                ->has('post')
                ->where('post.slug', $slug)
            );
        }
    }

    /**
     * Test that routes with special characters in slugs work.
     */
    public function test_routes_with_special_characters(): void
    {
        $specialSlugs = [
            'meta-ads-2024',
            'facebook-pixel-v2',
            'google-analytics-4',
        ];

        foreach ($specialSlugs as $slug) {
            $serviceResponse = $this->get("/services/{$slug}");
            $serviceResponse->assertStatus(200);
            
            $blogResponse = $this->get("/blog/{$slug}");
            $blogResponse->assertStatus(200);
        }
    }

    /**
     * Test that the application handles long slugs properly.
     */
    public function test_long_slug_handling(): void
    {
        $longSlug = 'this-is-a-very-long-slug-that-might-cause-issues-if-not-handled-properly-in-the-routing-system';
        
        $serviceResponse = $this->get("/services/{$longSlug}");
        $serviceResponse->assertStatus(200);
        $serviceResponse->assertInertia(fn ($page) => $page
            ->has('service')
            ->where('service.slug', $longSlug)
        );
        
        $blogResponse = $this->get("/blog/{$longSlug}");
        $blogResponse->assertStatus(200);
        $blogResponse->assertInertia(fn ($page) => $page
            ->has('post')
            ->where('post.slug', $longSlug)
        );
    }

    /**
     * Test that routes are case-sensitive.
     */
    public function test_route_case_sensitivity(): void
    {
        $response = $this->get('/ABOUT');
        // Should return 404 since routes are case-sensitive
        $response->assertStatus(404);
        
        $response = $this->get('/about');
        $response->assertStatus(200);
    }

    /**
     * Test that trailing slashes are handled correctly.
     */
    public function test_trailing_slash_handling(): void
    {
        // Test without trailing slash
        $response = $this->get('/about');
        $response->assertStatus(200);
        
        // Test with trailing slash - Laravel typically redirects
        $response = $this->get('/about/');
        // This might redirect to /about or return 404 depending on configuration
        $this->assertTrue(in_array($response->status(), [200, 301, 302, 404]));
    }
}
