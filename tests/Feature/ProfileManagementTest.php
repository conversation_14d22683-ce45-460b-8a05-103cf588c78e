<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ProfileManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_user_can_view_profile_page()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/settings/profile');

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('settings/profile')
        );
    }

    public function test_user_can_update_basic_profile_information()
    {
        $user = User::factory()->create([
            'name' => 'Old Name',
            'email' => '<EMAIL>',
        ]);

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => 'New Name',
            'email' => '<EMAIL>',
        ]);

        $response->assertRedirect('/settings/profile');
        
        $user->refresh();
        $this->assertEquals('New Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
    }

    public function test_user_can_update_additional_profile_fields()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => $user->name,
            'email' => $user->email,
            'phone' => '+1234567890',
            'company' => 'Test Company Inc.',
        ]);

        $response->assertRedirect('/settings/profile');
        
        $user->refresh();
        $this->assertEquals('+1234567890', $user->phone);
        $this->assertEquals('Test Company Inc.', $user->company);
    }

    public function test_profile_update_validates_required_fields()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => '',
            'email' => '',
        ]);

        $response->assertSessionHasErrors(['name', 'email']);
    }

    public function test_profile_update_validates_email_format()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => 'invalid-email',
        ]);

        $response->assertSessionHasErrors(['email']);
    }

    public function test_profile_update_validates_email_uniqueness()
    {
        $existingUser = User::factory()->create(['email' => '<EMAIL>']);
        $user = User::factory()->create();

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $response->assertSessionHasErrors(['email']);
    }

    public function test_user_can_keep_same_email_when_updating_profile()
    {
        $user = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => 'Updated Name',
            'email' => '<EMAIL>', // Same email
            'phone' => '+1234567890',
        ]);

        $response->assertRedirect('/settings/profile');
        $response->assertSessionHasNoErrors();
        
        $user->refresh();
        $this->assertEquals('Updated Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('+1234567890', $user->phone);
    }

    public function test_profile_update_validates_phone_length()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => str_repeat('1', 25), // Too long
        ]);

        $response->assertSessionHasErrors(['phone']);
    }

    public function test_profile_update_validates_company_length()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'company' => str_repeat('A', 300), // Too long
        ]);

        $response->assertSessionHasErrors(['company']);
    }

    public function test_profile_update_allows_null_optional_fields()
    {
        $user = User::factory()->create([
            'phone' => '+1234567890',
            'company' => 'Old Company',
        ]);

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => null,
            'company' => null,
        ]);

        $response->assertRedirect('/settings/profile');
        $response->assertSessionHasNoErrors();
        
        $user->refresh();
        $this->assertNull($user->phone);
        $this->assertNull($user->company);
    }

    public function test_profile_update_trims_whitespace()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => '  Test User  ',
            'email' => '  <EMAIL>  ',
            'phone' => '  +1234567890  ',
            'company' => '  Test Company  ',
        ]);

        $response->assertRedirect('/settings/profile');
        
        $user->refresh();
        $this->assertEquals('Test User', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('+1234567890', $user->phone);
        $this->assertEquals('Test Company', $user->company);
    }

    public function test_unauthenticated_user_cannot_access_profile()
    {
        $response = $this->get('/settings/profile');

        $response->assertRedirect('/login');
    }

    public function test_unauthenticated_user_cannot_update_profile()
    {
        $response = $this->patch('/settings/profile', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        $response->assertRedirect('/login');
    }

    public function test_profile_update_preserves_other_user_data()
    {
        $user = User::factory()->create([
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        $originalRole = $user->role;
        $originalVerifiedAt = $user->email_verified_at;

        $response = $this->actingAs($user)->patch('/settings/profile', [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
        ]);

        $response->assertRedirect('/settings/profile');

        $user->refresh();
        $this->assertEquals('Updated Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('+1234567890', $user->phone);
        $this->assertEquals($originalRole, $user->role);
        // When email is changed, email_verified_at is set to null
        $this->assertNull($user->email_verified_at);
    }
}
