<?php

namespace Tests\Feature;

use App\Models\Service;
use App\Models\TeamMember;
use App\Models\BlogPost;
use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PublicPagesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create services
        Service::factory()->create([
            'title' => 'Meta Ads Management',
            'slug' => 'meta-ads-management',
            'category' => 'Meta Advertising',
            'is_active' => true,
            'sort_order' => 1
        ]);

        Service::factory()->create([
            'title' => 'Facebook Pixel Setup',
            'slug' => 'facebook-pixel-setup',
            'category' => 'Tracking & Analytics',
            'is_active' => true,
            'sort_order' => 2
        ]);

        // Create team members
        TeamMember::factory()->create([
            'name' => '<PERSON>',
            'position' => 'Senior Meta Ads Specialist',
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Create categories and blog posts
        $category = Category::factory()->create([
            'name' => 'Meta Advertising',
            'slug' => 'meta-advertising',
            'is_active' => true
        ]);

        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Meta Ads Best Practices',
            'slug' => 'meta-ads-best-practices',
            'excerpt' => 'Learn the best practices for Meta advertising campaigns.',
            'content' => 'This is a comprehensive guide to Meta advertising best practices...',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);
    }

    /** @test */
    public function home_page_loads_successfully()
    {
        $response = $this->get('/');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/home')
                ->has('services')
                ->has('teamMembers')
                ->has('blogPosts')
                ->has('meta')
                ->has('structuredData')
        );
    }

    /** @test */
    public function services_page_loads_successfully()
    {
        $response = $this->get('/services');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/services')
                ->has('services')
                ->has('categories')
                ->has('filters')
                ->has('meta')
        );
    }

    /** @test */
    public function services_search_works()
    {
        $response = $this->get('/services?search=meta');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/services')
                ->where('filters.search', 'meta')
        );
    }

    /** @test */
    public function services_category_filter_works()
    {
        $response = $this->get('/services?category=Meta Advertising');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/services')
                ->where('filters.category', 'Meta Advertising')
        );
    }

    /** @test */
    public function service_detail_page_loads_successfully()
    {
        $response = $this->get('/services/meta-ads-management');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/service-detail')
                ->has('service')
                ->has('relatedServices')
                ->has('meta')
                ->has('structuredData')
                ->has('breadcrumbs')
        );
    }

    /** @test */
    public function service_detail_page_returns_404_for_invalid_slug()
    {
        $response = $this->get('/services/non-existent-service');
        
        $response->assertStatus(404);
    }

    /** @test */
    public function team_page_loads_successfully()
    {
        $response = $this->get('/team');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/team')
                ->has('teamMembers')
                ->has('meta')
        );
    }

    /** @test */
    public function blog_page_loads_successfully()
    {
        $response = $this->get('/blog');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/blog')
                ->has('blogPosts')
                ->has('categories')
                ->has('filters')
                ->has('meta')
        );
    }

    /** @test */
    public function blog_search_works()
    {
        $response = $this->get('/blog?search=meta');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/blog')
                ->where('filters.search', 'meta')
        );
    }

    /** @test */
    public function blog_category_filter_works()
    {
        $category = Category::first();
        $response = $this->get('/blog?category_id=' . $category->id);
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/blog')
                ->where('filters.category_id', (string)$category->id)
        );
    }

    /** @test */
    public function blog_post_detail_page_loads_successfully()
    {
        $response = $this->get('/blog/meta-ads-best-practices');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/blog-post')
                ->has('post')
                ->has('relatedPosts')
                ->has('meta')
                ->has('structuredData')
                ->has('breadcrumbs')
        );
    }

    /** @test */
    public function blog_post_detail_page_returns_404_for_invalid_slug()
    {
        $response = $this->get('/blog/non-existent-post');
        
        $response->assertStatus(404);
    }

    /** @test */
    public function about_page_loads_successfully()
    {
        $response = $this->get('/about');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/about')
                ->has('meta')
        );
    }

    /** @test */
    public function contact_page_loads_successfully()
    {
        $response = $this->get('/contact');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/contact')
                ->has('meta')
        );
    }

    /** @test */
    public function book_consultation_page_loads_successfully()
    {
        $response = $this->get('/book-consultation');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/book-consultation')
                ->has('services')
                ->has('meta')
        );
    }

    /** @test */
    public function all_pages_have_proper_seo_meta_tags()
    {
        $pages = [
            '/' => 'ConvertOKit - Meta Ads & Web Analytics Specialists',
            '/services' => 'Our Services - ConvertOKit',
            '/team' => 'Our Team - ConvertOKit',
            '/blog' => 'Blog - ConvertOKit',
            '/about' => 'About Us - ConvertOKit',
            '/contact' => 'Contact Us - ConvertOKit',
            '/book-consultation' => 'Book Consultation - ConvertOKit',
        ];

        foreach ($pages as $url => $expectedTitle) {
            $response = $this->get($url);
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => 
                $page->has('meta')
                    ->where('meta.title', $expectedTitle)
                    ->has('meta.description')
                    ->has('meta.canonical')
            );
        }
    }

    /** @test */
    public function navigation_links_work_correctly()
    {
        // Test navigation from home page
        $response = $this->get('/');
        $response->assertStatus(200);

        // Test all main navigation links
        $navLinks = [
            '/services',
            '/team', 
            '/blog',
            '/about',
            '/contact',
            '/book-consultation'
        ];

        foreach ($navLinks as $link) {
            $response = $this->get($link);
            $response->assertStatus(200);
        }
    }

    /** @test */
    public function structured_data_is_present_on_relevant_pages()
    {
        // Home page should have organization structured data
        $response = $this->get('/');
        $response->assertInertia(fn ($page) => 
            $page->has('structuredData')
        );

        // Service detail page should have service structured data
        $response = $this->get('/services/meta-ads-management');
        $response->assertInertia(fn ($page) => 
            $page->has('structuredData')
        );

        // Blog post should have article structured data
        $response = $this->get('/blog/meta-ads-best-practices');
        $response->assertInertia(fn ($page) => 
            $page->has('structuredData')
        );
    }
}
