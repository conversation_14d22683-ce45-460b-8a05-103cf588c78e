<?php

namespace Tests\Feature;

use App\Models\Service;
use App\Models\TeamMember;
use App\Models\BlogPost;
use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PublicControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create services
        Service::factory()->create([
            'title' => 'Meta Ads Management',
            'slug' => 'meta-ads-management',
            'category' => 'Meta Advertising',
            'description' => 'Professional Meta advertising management',
            'is_active' => true,
            'sort_order' => 1
        ]);

        Service::factory()->create([
            'title' => 'Facebook Pixel Setup',
            'slug' => 'facebook-pixel-setup',
            'category' => 'Tracking & Analytics',
            'description' => 'Professional Facebook Pixel implementation',
            'is_active' => true,
            'sort_order' => 2
        ]);

        // Create team members
        TeamMember::factory()->create([
            'name' => '<PERSON>',
            'position' => 'Senior Meta Ads Specialist',
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Create categories and blog posts
        $category = Category::factory()->create([
            'name' => 'Meta Advertising',
            'slug' => 'meta-advertising',
            'is_active' => true
        ]);

        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Meta Ads Best Practices',
            'slug' => 'meta-ads-best-practices',
            'excerpt' => 'Learn the best practices for Meta advertising',
            'content' => 'Comprehensive guide to Meta advertising...',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);
    }

    public function test_home_page_returns_correct_data()
    {
        $response = $this->get('/');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/home')
                ->has('services')
                ->has('teamMembers')
                ->has('blogPosts')
                ->has('meta')
                ->has('structuredData')
                ->where('meta.title', 'ConvertOKit - Meta Ads & Web Analytics Specialists')
                ->where('structuredData.@type', 'Organization')
        );
    }

    public function test_services_page_returns_correct_data()
    {
        $response = $this->get('/services');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/services')
                ->has('services', 2)
                ->has('categories')
                ->has('filters')
                ->has('meta')
                ->where('meta.title', 'Our Services - ConvertOKit')
        );
    }

    public function test_services_search_functionality()
    {
        $response = $this->get('/services?search=meta');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/services')
                ->where('filters.search', 'meta')
                ->has('services', 1)
        );
    }

    public function test_services_category_filter()
    {
        $response = $this->get('/services?category=Meta Advertising');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/services')
                ->where('filters.category', 'Meta Advertising')
                ->has('services', 1)
        );
    }

    public function test_service_detail_page_returns_correct_data()
    {
        $service = Service::where('slug', 'meta-ads-management')->first();
        $response = $this->get('/services/meta-ads-management');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('public/service-detail')
                ->has('service')
                ->has('relatedServices')
                ->has('meta')
                ->has('structuredData')
                ->has('breadcrumbs')
                ->where('service.title', $service->title)
                ->where('service.slug', 'meta-ads-management')
        );
    }

    public function test_service_detail_page_404_for_invalid_slug()
    {
        $response = $this->get('/services/non-existent-service');
        $response->assertStatus(404);
    }

    public function test_team_page_returns_correct_data()
    {
        $response = $this->get('/team');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/team')
                ->has('teamMembers', 1)
                ->has('meta')
                ->where('meta.title', 'Our Team - ConvertOKit')
        );
    }

    public function test_blog_page_returns_correct_data()
    {
        $response = $this->get('/blog');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/blog')
                ->has('blogPosts')
                ->has('categories')
                ->has('filters')
                ->has('meta')
                ->where('meta.title', 'Blog - ConvertOKit')
        );
    }

    public function test_blog_search_functionality()
    {
        $response = $this->get('/blog?search=meta');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/blog')
                ->where('filters.search', 'meta')
        );
    }

    public function test_blog_category_filter()
    {
        $category = Category::first();
        $response = $this->get('/blog?category_id=' . $category->id);
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/blog')
                ->where('filters.category_id', (string)$category->id)
        );
    }

    public function test_blog_post_detail_page_returns_correct_data()
    {
        $response = $this->get('/blog/meta-ads-best-practices');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/blog-post')
                ->has('post')
                ->has('relatedPosts')
                ->has('meta')
                ->has('structuredData')
                ->has('breadcrumbs')
                ->where('post.title', 'Meta Ads Best Practices')
        );
    }

    public function test_blog_post_detail_page_404_for_invalid_slug()
    {
        $response = $this->get('/blog/non-existent-post');
        $response->assertStatus(404);
    }

    public function test_blog_post_view_count_increments()
    {
        $post = BlogPost::first();
        $initialViews = $post->views_count ?? 0;
        
        $this->get('/blog/' . $post->slug);
        
        $post->refresh();
        $this->assertEquals($initialViews + 1, $post->views_count);
    }

    public function test_about_page_returns_correct_data()
    {
        $response = $this->get('/about');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/about')
                ->has('meta')
                ->where('meta.title', 'About Us - ConvertOKit')
        );
    }

    public function test_contact_page_returns_correct_data()
    {
        $response = $this->get('/contact');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/contact')
                ->has('meta')
                ->where('meta.title', 'Contact Us - ConvertOKit')
        );
    }

    public function test_book_consultation_page_returns_correct_data()
    {
        $response = $this->get('/book-consultation');
        
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('public/book-consultation')
                ->has('services')
                ->has('meta')
                ->where('meta.title', 'Book Consultation - ConvertOKit')
        );
    }

    public function test_all_pages_have_proper_seo_meta_structure()
    {
        $pages = [
            '/' => 'ConvertOKit - Meta Ads & Web Analytics Specialists',
            '/services' => 'Our Services - ConvertOKit',
            '/team' => 'Our Team - ConvertOKit',
            '/blog' => 'Blog - ConvertOKit',
            '/about' => 'About Us - ConvertOKit',
            '/contact' => 'Contact Us - ConvertOKit',
            '/book-consultation' => 'Book Consultation - ConvertOKit',
        ];

        foreach ($pages as $url => $expectedTitle) {
            $response = $this->get($url);
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) =>
                $page->has('meta')
                    ->where('meta.title', $expectedTitle)
                    ->has('meta.description')
                    ->has('meta.canonical')
            );
        }
    }

    public function test_structured_data_is_present_on_relevant_pages()
    {
        // Home page should have organization structured data
        $response = $this->get('/');
        $response->assertInertia(fn ($page) => 
            $page->has('structuredData')
                ->where('structuredData.@type', 'Organization')
        );

        // Service detail page should have service structured data
        $response = $this->get('/services/meta-ads-management');
        $response->assertInertia(fn ($page) => 
            $page->has('structuredData')
        );

        // Blog post should have article structured data
        $response = $this->get('/blog/meta-ads-best-practices');
        $response->assertInertia(fn ($page) => 
            $page->has('structuredData')
        );
    }

    public function test_error_handling_with_empty_database()
    {
        // Clear all data
        Service::truncate();
        TeamMember::truncate();
        BlogPost::truncate();
        Category::truncate();

        // Pages should still load without errors
        $this->get('/')->assertStatus(200);
        $this->get('/services')->assertStatus(200);
        $this->get('/team')->assertStatus(200);
        $this->get('/blog')->assertStatus(200);
    }
}
