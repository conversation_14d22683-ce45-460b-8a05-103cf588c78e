<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RoleBasedAccessTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_admin_middleware_allows_admin_users()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertOk();
    }

    public function test_admin_middleware_blocks_client_users()
    {
        $client = User::factory()->create(['role' => 'client']);

        $response = $this->actingAs($client)->get('/admin/dashboard');

        $response->assertForbidden();
    }

    public function test_admin_middleware_blocks_team_member_users()
    {
        $teamMember = User::factory()->create(['role' => 'team_member']);

        $response = $this->actingAs($teamMember)->get('/admin/dashboard');

        $response->assertForbidden();
    }

    public function test_admin_middleware_blocks_unauthenticated_users()
    {
        $response = $this->get('/admin/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_all_admin_routes_require_admin_role()
    {
        $client = User::factory()->create(['role' => 'client']);
        $teamMember = User::factory()->create(['role' => 'team_member']);

        $adminRoutes = [
            '/admin/dashboard',
            '/admin/users',
            '/admin/services',
            '/admin/blog',
            '/admin/team',
            '/admin/consultations',
            '/admin/payments',
            '/admin/analytics',
            '/admin/contacts',
            '/admin/newsletter',
            '/admin/settings',
        ];

        foreach ($adminRoutes as $route) {
            // Test client access
            $response = $this->actingAs($client)->get($route);
            $response->assertForbidden("Client should not access {$route}");

            // Test team member access
            $response = $this->actingAs($teamMember)->get($route);
            $response->assertForbidden("Team member should not access {$route}");
        }
    }

    public function test_admin_can_access_all_admin_routes()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $adminRoutes = [
            '/admin/dashboard',
            '/admin/users',
            '/admin/services',
            '/admin/blog',
            '/admin/team',
            '/admin/consultations',
            '/admin/payments',
            '/admin/analytics',
            '/admin/contacts',
            '/admin/newsletter',
            '/admin/settings',
        ];

        foreach ($adminRoutes as $route) {
            $response = $this->actingAs($admin)->get($route);
            $response->assertOk("Admin should access {$route}");
        }
    }

    public function test_dashboard_redirects_admin_to_admin_panel()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/dashboard');

        $response->assertRedirect('/admin/dashboard');
    }

    public function test_dashboard_allows_client_access()
    {
        $client = User::factory()->create(['role' => 'client']);

        $response = $this->actingAs($client)->get('/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard')
                ->where('role', 'client')
        );
    }

    public function test_dashboard_allows_team_member_access()
    {
        $teamMember = User::factory()->create(['role' => 'team_member']);

        $response = $this->actingAs($teamMember)->get('/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard')
                ->where('role', 'team_member')
        );
    }

    public function test_settings_routes_require_authentication()
    {
        $settingsRoutes = [
            '/settings/profile',
            '/settings/password',
        ];

        foreach ($settingsRoutes as $route) {
            $response = $this->get($route);
            $response->assertRedirect('/login');
        }
    }

    public function test_all_user_roles_can_access_settings()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $client = User::factory()->create(['role' => 'client']);
        $teamMember = User::factory()->create(['role' => 'team_member']);

        $users = [$admin, $client, $teamMember];

        foreach ($users as $user) {
            $response = $this->actingAs($user)->get('/settings/profile');
            $response->assertOk("User with role {$user->role} should access settings");
        }
    }

    public function test_role_based_navigation_data()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $client = User::factory()->create(['role' => 'client']);
        $teamMember = User::factory()->create(['role' => 'team_member']);

        // Test admin navigation
        $response = $this->actingAs($admin)->get('/dashboard');
        $response->assertRedirect('/admin/dashboard');

        // Test client navigation
        $response = $this->actingAs($client)->get('/dashboard');
        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->where('role', 'client')
        );

        // Test team member navigation
        $response = $this->actingAs($teamMember)->get('/dashboard');
        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->where('role', 'team_member')
        );
    }

    public function test_middleware_preserves_intended_url()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        // Try to access admin route without authentication
        $response = $this->get('/admin/users');
        $response->assertRedirect('/login');

        // Login and should be redirected to intended URL
        $response = $this->post('/login', [
            'email' => $admin->email,
            'password' => 'password',
        ]);

        $response->assertRedirect('/admin/users');
    }

    public function test_api_routes_respect_role_permissions()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $client = User::factory()->create(['role' => 'client']);

        // Admin should access admin API routes
        $response = $this->actingAs($admin)->get('/api/admin/users');
        $response->assertOk();

        // Client should not access admin API routes
        $response = $this->actingAs($client)->get('/api/admin/users');
        $response->assertForbidden();
    }

    public function test_role_validation_is_case_sensitive()
    {
        // Test that database constraint prevents invalid role values
        $this->expectException(\Illuminate\Database\QueryException::class);

        // Attempt to create user with uppercase role (should fail at database level)
        User::factory()->create(['role' => 'ADMIN']);
    }

    public function test_invalid_role_cannot_access_protected_routes()
    {
        // Test that database constraint prevents invalid role values
        $this->expectException(\Illuminate\Database\QueryException::class);

        // Attempt to create user with invalid role (should fail at database level)
        User::factory()->create(['role' => 'invalid_role']);
    }
}
