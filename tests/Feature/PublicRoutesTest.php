<?php

namespace Tests\Feature;

use App\Models\BlogPost;
use App\Models\Category;
use App\Models\Service;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PublicRoutesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create services
        Service::factory()->create([
            'title' => 'Meta Ads Management',
            'slug' => 'meta-ads-management',
            'category' => 'Meta Advertising',
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Create categories and blog posts
        $category = Category::factory()->create([
            'name' => 'Meta Advertising',
            'slug' => 'meta-advertising',
            'is_active' => true
        ]);

        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Sample Post',
            'slug' => 'sample-post',
            'excerpt' => 'Sample post excerpt',
            'content' => 'Sample post content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);
    }

    /**
     * Test that the home page loads successfully.
     */
    public function test_home_page_loads_successfully(): void
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/home')
        );
    }

    /**
     * Test that the about page loads successfully.
     */
    public function test_about_page_loads_successfully(): void
    {
        $response = $this->get('/about');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/about')
        );
    }

    /**
     * Test that the services page loads successfully.
     */
    public function test_services_page_loads_successfully(): void
    {
        $response = $this->get('/services');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/services')
        );
    }

    /**
     * Test that individual service pages load successfully.
     */
    public function test_service_detail_page_loads_successfully(): void
    {
        $response = $this->get('/services/meta-ads-management');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/service-detail')
            ->has('service')
            ->where('service.slug', 'meta-ads-management')
        );
    }

    /**
     * Test that the team page loads successfully.
     */
    public function test_team_page_loads_successfully(): void
    {
        $response = $this->get('/team');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/team')
        );
    }

    /**
     * Test that the blog page loads successfully.
     */
    public function test_blog_page_loads_successfully(): void
    {
        $response = $this->get('/blog');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/blog')
        );
    }

    /**
     * Test that individual blog post pages load successfully.
     */
    public function test_blog_post_page_loads_successfully(): void
    {
        $response = $this->get('/blog/sample-post');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/blog-post')
            ->has('post')
            ->where('post.slug', 'sample-post')
        );
    }

    /**
     * Test that the contact page loads successfully.
     */
    public function test_contact_page_loads_successfully(): void
    {
        $response = $this->get('/contact');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/contact')
        );
    }

    /**
     * Test that the consultation booking page loads successfully.
     */
    public function test_consultation_booking_page_loads_successfully(): void
    {
        $response = $this->get('/book-consultation');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/book-consultation')
        );
    }

    /**
     * Test that the legacy welcome route still works.
     */
    public function test_legacy_welcome_route_works(): void
    {
        $response = $this->get('/welcome');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('welcome')
        );
    }

    /**
     * Test that all public routes are accessible without authentication.
     */
    public function test_public_routes_accessible_without_authentication(): void
    {
        $publicRoutes = [
            '/',
            '/about',
            '/services',
            '/services/meta-ads-management',
            '/team',
            '/blog',
            '/blog/sample-post',
            '/contact',
            '/book-consultation',
        ];

        foreach ($publicRoutes as $route) {
            $response = $this->get($route);
            $response->assertStatus(200);
        }
    }

    /**
     * Test that public routes have proper SEO structure.
     */
    public function test_public_routes_have_proper_structure(): void
    {
        $routes = [
            '/' => 'public/home',
            '/about' => 'public/about',
            '/services' => 'public/services',
            '/team' => 'public/team',
            '/blog' => 'public/blog',
            '/contact' => 'public/contact',
            '/book-consultation' => 'public/book-consultation',
        ];

        foreach ($routes as $route => $component) {
            $response = $this->get($route);
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page->component($component));
        }
    }

    /**
     * Test that service detail routes pass correct slug parameter.
     */
    public function test_service_detail_routes_pass_slug_parameter(): void
    {
        $slugs = ['meta-ads-management'];

        foreach ($slugs as $slug) {
            $response = $this->get("/services/{$slug}");
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page
                ->component('public/service-detail')
                ->has('service')
                ->where('service.slug', $slug)
            );
        }
    }

    /**
     * Test that blog post routes pass correct slug parameter.
     */
    public function test_blog_post_routes_pass_slug_parameter(): void
    {
        $slugs = ['sample-post'];

        foreach ($slugs as $slug) {
            $response = $this->get("/blog/{$slug}");
            $response->assertStatus(200);
            $response->assertInertia(fn ($page) => $page
                ->component('public/blog-post')
                ->has('post')
                ->where('post.slug', $slug)
            );
        }
    }
}
