<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Consultation;
use App\Models\Payment;
use App\Models\Service;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DashboardControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_admin_user_redirected_to_admin_dashboard()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/dashboard');

        $response->assertRedirect();
        $this->assertStringContainsString('admin/dashboard', $response->headers->get('Location'));
    }

    public function test_client_user_sees_client_dashboard()
    {
        $client = User::factory()->create(['role' => 'client']);
        
        // Create some test data
        $service = Service::factory()->create();
        $consultation = Consultation::factory()->create([
            'user_id' => $client->id,
            'service_id' => $service->id,
            'status' => 'confirmed',
            'consultation_date' => now()->addDays(1),
        ]);
        $payment = Payment::factory()->create([
            'user_id' => $client->id,
            'consultation_id' => $consultation->id,
            'status' => 'completed',
            'amount' => 100.00,
        ]);

        $response = $this->actingAs($client)->get('/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard')
                ->has('user')
                ->has('role')
                ->has('stats')
                ->has('upcoming_consultations')
                ->has('recent_consultations')
                ->has('recent_payments')
                ->where('role', 'client')
                ->where('user.id', $client->id)
        );
    }

    public function test_team_member_user_sees_team_dashboard()
    {
        $teamMember = User::factory()->create(['role' => 'team_member']);

        $response = $this->actingAs($teamMember)->get('/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->component('dashboard')
                ->has('user')
                ->has('role')
                ->has('stats')
                ->has('assigned_consultations')
                ->where('role', 'team_member')
                ->where('user.id', $teamMember->id)
        );
    }

    public function test_client_dashboard_shows_correct_stats()
    {
        $client = User::factory()->create(['role' => 'client']);
        $service = Service::factory()->create();
        
        // Create consultations
        $completedConsultation = Consultation::factory()->create([
            'user_id' => $client->id,
            'service_id' => $service->id,
            'status' => 'completed',
        ]);
        
        $upcomingConsultation = Consultation::factory()->create([
            'user_id' => $client->id,
            'service_id' => $service->id,
            'status' => 'confirmed',
            'consultation_date' => now()->addDays(1),
        ]);

        // Create payments
        $payment = Payment::factory()->create([
            'user_id' => $client->id,
            'consultation_id' => $completedConsultation->id,
            'status' => 'completed',
            'amount' => 150.00,
        ]);

        $response = $this->actingAs($client)->get('/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->where('stats.total_consultations', 2)
                ->where('stats.upcoming_consultations', 1)
                ->where('stats.completed_consultations', 1)
                ->where('stats.total_spent', 150)
                ->where('stats.pending_payments', 0)
        );
    }

    public function test_team_member_dashboard_shows_correct_stats()
    {
        $teamMember = User::factory()->create(['role' => 'team_member']);
        $client = User::factory()->create(['role' => 'client']);
        $service = Service::factory()->create();
        
        // Create consultations for this month
        Consultation::factory()->create([
            'user_id' => $client->id,
            'service_id' => $service->id,
            'status' => 'completed',
            'created_at' => now(),
        ]);

        Consultation::factory()->create([
            'user_id' => $client->id,
            'service_id' => $service->id,
            'status' => 'confirmed',
        ]);

        $response = $this->actingAs($teamMember)->get('/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->where('stats.assigned_consultations', 2)
                ->where('stats.completed_this_month', 1)
        );
    }

    public function test_unauthenticated_user_redirected_to_login()
    {
        $response = $this->get('/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_dashboard_loads_related_data_efficiently()
    {
        $client = User::factory()->create(['role' => 'client']);
        $service = Service::factory()->create();
        
        // Create multiple consultations and payments
        for ($i = 0; $i < 5; $i++) {
            $consultation = Consultation::factory()->create([
                'user_id' => $client->id,
                'service_id' => $service->id,
            ]);
            
            Payment::factory()->create([
                'user_id' => $client->id,
                'consultation_id' => $consultation->id,
            ]);
        }

        // Track database queries
        $queryCount = 0;
        \DB::listen(function ($query) use (&$queryCount) {
            $queryCount++;
        });

        $response = $this->actingAs($client)->get('/dashboard');

        $response->assertOk();
        
        // Ensure we're not making excessive queries (should be under 10)
        $this->assertLessThan(10, $queryCount, 'Dashboard should load efficiently with minimal queries');
    }
}
