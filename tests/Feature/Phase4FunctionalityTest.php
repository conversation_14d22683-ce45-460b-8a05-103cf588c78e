<?php

namespace Tests\Feature;

use App\Models\Service;
use App\Models\TeamMember;
use App\Models\BlogPost;
use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class Phase4FunctionalityTest extends TestCase
{
    use RefreshDatabase;

    public function test_public_controller_methods_exist()
    {
        $this->assertTrue(method_exists(\App\Http\Controllers\PublicController::class, 'home'));
        $this->assertTrue(method_exists(\App\Http\Controllers\PublicController::class, 'services'));
        $this->assertTrue(method_exists(\App\Http\Controllers\PublicController::class, 'serviceDetail'));
        $this->assertTrue(method_exists(\App\Http\Controllers\PublicController::class, 'team'));
        $this->assertTrue(method_exists(\App\Http\Controllers\PublicController::class, 'blog'));
        $this->assertTrue(method_exists(\App\Http\Controllers\PublicController::class, 'blogPost'));
        $this->assertTrue(method_exists(\App\Http\Controllers\PublicController::class, 'about'));
        $this->assertTrue(method_exists(\App\Http\Controllers\PublicController::class, 'contact'));
        $this->assertTrue(method_exists(\App\Http\Controllers\PublicController::class, 'bookConsultation'));
    }

    public function test_seo_service_exists()
    {
        $this->assertTrue(class_exists(\App\Services\SeoService::class));
        
        $seoService = new \App\Services\SeoService();
        $this->assertTrue(method_exists($seoService, 'generateMetaTags'));
        $this->assertTrue(method_exists($seoService, 'getHomeStructuredData'));
        $this->assertTrue(method_exists($seoService, 'getServiceStructuredData'));
        $this->assertTrue(method_exists($seoService, 'getBlogPostStructuredData'));
    }

    public function test_blog_service_has_required_methods()
    {
        $blogService = new \App\Services\BlogService();
        $this->assertTrue(method_exists($blogService, 'getActiveCategories'));
        $this->assertTrue(method_exists($blogService, 'findBySlug'));
        $this->assertTrue(method_exists($blogService, 'incrementViews'));
    }

    public function test_service_service_has_required_methods()
    {
        $serviceService = new \App\Services\ServiceService();
        $this->assertTrue(method_exists($serviceService, 'getActiveServices'));
        $this->assertTrue(method_exists($serviceService, 'findBySlug'));
    }

    public function test_routes_are_properly_defined()
    {
        $routes = [
            '/' => 'GET',
            '/services' => 'GET',
            '/services/{slug}' => 'GET',
            '/team' => 'GET',
            '/blog' => 'GET',
            '/blog/{slug}' => 'GET',
            '/about' => 'GET',
            '/contact' => 'GET',
            '/book-consultation' => 'GET',
        ];

        foreach ($routes as $uri => $method) {
            $route = \Illuminate\Support\Facades\Route::getRoutes()->match(
                \Illuminate\Http\Request::create($uri, $method)
            );
            $this->assertNotNull($route, "Route {$method} {$uri} should exist");
        }
    }

    public function test_seo_meta_tags_generation()
    {
        $seoService = new \App\Services\SeoService();
        
        $meta = $seoService->generateMetaTags([
            'title' => 'Test Title',
            'description' => 'Test Description',
            'canonical' => 'https://example.com'
        ]);

        $this->assertArrayHasKey('title', $meta);
        $this->assertArrayHasKey('description', $meta);
        $this->assertArrayHasKey('canonical', $meta);
        $this->assertArrayHasKey('og_title', $meta);
        $this->assertArrayHasKey('og_description', $meta);
        $this->assertArrayHasKey('twitter_card', $meta);
        
        $this->assertEquals('Test Title', $meta['title']);
        $this->assertEquals('Test Description', $meta['description']);
        $this->assertEquals('https://example.com', $meta['canonical']);
    }

    public function test_structured_data_generation()
    {
        $seoService = new \App\Services\SeoService();
        
        // Test home structured data
        $homeData = $seoService->getHomeStructuredData([], []);
        $this->assertArrayHasKey('@context', $homeData);
        $this->assertArrayHasKey('@type', $homeData);
        $this->assertEquals('https://schema.org', $homeData['@context']);
        $this->assertEquals('Organization', $homeData['@type']);

        // Test breadcrumb structured data
        $breadcrumbs = [
            ['name' => 'Home', 'url' => '/'],
            ['name' => 'Services', 'url' => '/services']
        ];
        $breadcrumbData = $seoService->getBreadcrumbStructuredData($breadcrumbs);
        $this->assertArrayHasKey('@context', $breadcrumbData);
        $this->assertArrayHasKey('@type', $breadcrumbData);
        $this->assertEquals('BreadcrumbList', $breadcrumbData['@type']);
    }

    public function test_search_and_filtering_parameters()
    {
        // Create test data
        Service::factory()->create([
            'title' => 'Meta Ads Management',
            'category' => 'Meta Advertising',
            'is_active' => true
        ]);

        $category = Category::factory()->create(['name' => 'Digital Marketing', 'is_active' => true]);
        $user = User::factory()->create();
        
        BlogPost::factory()->create([
            'title' => 'Test Blog Post',
            'excerpt' => 'Test excerpt',
            'content' => 'Test content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        // Test service search
        $serviceService = new \App\Services\ServiceService();
        $services = $serviceService->getActiveServices(['search' => 'Meta']);
        $this->assertNotEmpty($services);

        // Test blog search
        $blogService = new \App\Services\BlogService();
        $posts = $blogService->getPublishedPosts(['search' => 'Test']);
        $this->assertNotEmpty($posts);
    }

    public function test_pagination_functionality()
    {
        $category = Category::factory()->create(['name' => 'Test Category', 'is_active' => true]);
        $user = User::factory()->create();

        // Create multiple blog posts for pagination testing
        for ($i = 1; $i <= 15; $i++) {
            BlogPost::factory()->create([
                'title' => "Test Post {$i}",
                'excerpt' => "Test excerpt {$i}",
                'content' => "Test content {$i}",
                'status' => 'published',
                'published_at' => now()->subDays($i),
                'category_id' => $category->id,
                'author_id' => $user->id
            ]);
        }

        $blogService = new \App\Services\BlogService();
        $posts = $blogService->getPublishedPosts([
            'paginate' => true,
            'limit' => 10
        ]);

        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $posts);
        $this->assertEquals(10, $posts->perPage());
        $this->assertEquals(15, $posts->total());
        $this->assertEquals(2, $posts->lastPage());
    }

    public function test_view_increment_functionality()
    {
        $category = Category::factory()->create(['name' => 'Test Category', 'is_active' => true]);
        $user = User::factory()->create();
        
        $post = BlogPost::factory()->create([
            'title' => 'Test Post',
            'excerpt' => 'Test excerpt',
            'content' => 'Test content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id,
            'views_count' => 0
        ]);

        $blogService = new \App\Services\BlogService();
        $blogService->incrementViews($post->id);

        $post->refresh();
        $this->assertEquals(1, $post->views_count);
    }

    public function test_slug_based_retrieval()
    {
        // Test service slug retrieval
        $service = Service::factory()->create([
            'title' => 'Test Service',
            'slug' => 'test-service',
            'is_active' => true
        ]);

        $serviceService = new \App\Services\ServiceService();
        $foundService = $serviceService->findBySlug('test-service');
        $this->assertNotNull($foundService);
        $this->assertEquals('Test Service', $foundService->title);

        // Test blog post slug retrieval
        $category = Category::factory()->create(['name' => 'Test Category', 'is_active' => true]);
        $user = User::factory()->create();
        
        $post = BlogPost::factory()->create([
            'title' => 'Test Blog Post',
            'slug' => 'test-blog-post',
            'excerpt' => 'Test excerpt',
            'content' => 'Test content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        $blogService = new \App\Services\BlogService();
        $foundPost = $blogService->findBySlug('test-blog-post');
        $this->assertNotNull($foundPost);
        $this->assertEquals('Test Blog Post', $foundPost->title);
    }

    public function test_category_filtering()
    {
        $category1 = Category::factory()->create(['name' => 'Category 1', 'is_active' => true]);
        $category2 = Category::factory()->create(['name' => 'Category 2', 'is_active' => true]);
        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Post in Category 1',
            'excerpt' => 'Test excerpt',
            'content' => 'Test content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category1->id,
            'author_id' => $user->id
        ]);

        BlogPost::factory()->create([
            'title' => 'Post in Category 2',
            'excerpt' => 'Test excerpt',
            'content' => 'Test content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category2->id,
            'author_id' => $user->id
        ]);

        $blogService = new \App\Services\BlogService();
        
        $category1Posts = $blogService->getPublishedPosts(['category_id' => $category1->id]);
        $this->assertCount(1, $category1Posts);
        $this->assertEquals('Post in Category 1', $category1Posts->first()->title);

        $category2Posts = $blogService->getPublishedPosts(['category_id' => $category2->id]);
        $this->assertCount(1, $category2Posts);
        $this->assertEquals('Post in Category 2', $category2Posts->first()->title);
    }
}
