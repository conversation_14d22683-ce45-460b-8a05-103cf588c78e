<?php

namespace Tests\Unit;

use App\Services\SeoService;
use App\Models\Service;
use App\Models\BlogPost;
use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SeoServiceTest extends TestCase
{
    use RefreshDatabase;

    private SeoService $seoService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seoService = new SeoService();
    }

    public function test_generate_meta_tags_with_basic_data()
    {
        $data = [
            'title' => 'Test Title',
            'description' => 'Test Description',
            'canonical' => 'https://example.com/test'
        ];

        $meta = $this->seoService->generateMetaTags($data);

        $this->assertArrayHasKey('title', $meta);
        $this->assertArrayHasKey('description', $meta);
        $this->assertArrayHasKey('canonical', $meta);
        $this->assertArrayHasKey('og_title', $meta);
        $this->assertArrayHasKey('og_description', $meta);
        $this->assertArrayHasKey('og_type', $meta);
        $this->assertArrayHasKey('twitter_card', $meta);

        $this->assertEquals('Test Title', $meta['title']);
        $this->assertEquals('Test Description', $meta['description']);
        $this->assertEquals('https://example.com/test', $meta['canonical']);
        $this->assertEquals('website', $meta['og_type']);
        $this->assertEquals('summary_large_image', $meta['twitter_card']);
    }

    public function test_generate_meta_tags_with_complete_data()
    {
        $data = [
            'title' => 'Complete Test Title',
            'description' => 'Complete Test Description',
            'keywords' => 'test, seo, meta',
            'canonical' => 'https://example.com/complete',
            'og_image' => 'https://example.com/image.jpg',
            'og_type' => 'article',
            'published_time' => '2025-01-01T00:00:00Z',
            'modified_time' => '2025-01-02T00:00:00Z',
            'author' => 'John Doe'
        ];

        $meta = $this->seoService->generateMetaTags($data);

        $this->assertEquals('Complete Test Title', $meta['title']);
        $this->assertEquals('Complete Test Description', $meta['description']);
        $this->assertEquals('test, seo, meta', $meta['keywords']);
        $this->assertEquals('https://example.com/image.jpg', $meta['og_image']);
        $this->assertEquals('article', $meta['og_type']);
        $this->assertEquals('2025-01-01T00:00:00Z', $meta['article_published_time']);
        $this->assertEquals('2025-01-02T00:00:00Z', $meta['article_modified_time']);
        $this->assertEquals('John Doe', $meta['article_author']);
    }

    public function test_generate_meta_tags_with_defaults()
    {
        $data = [];

        $meta = $this->seoService->generateMetaTags($data);

        $this->assertEquals('ConvertOKit', $meta['title']);
        $this->assertEquals('Expert Meta advertising and web analytics services', $meta['description']);
        $this->assertEquals('Meta ads, Facebook advertising, web analytics', $meta['keywords']);
        $this->assertEquals('website', $meta['og_type']);
        $this->assertEquals('ConvertOKit', $meta['og_site_name']);
    }

    public function test_get_home_structured_data_basic()
    {
        $structuredData = $this->seoService->getHomeStructuredData();

        $this->assertArrayHasKey('@context', $structuredData);
        $this->assertArrayHasKey('@type', $structuredData);
        $this->assertArrayHasKey('name', $structuredData);
        $this->assertArrayHasKey('description', $structuredData);
        $this->assertArrayHasKey('url', $structuredData);
        $this->assertArrayHasKey('contactPoint', $structuredData);
        $this->assertArrayHasKey('address', $structuredData);
        $this->assertArrayHasKey('sameAs', $structuredData);

        $this->assertEquals('https://schema.org', $structuredData['@context']);
        $this->assertEquals('Organization', $structuredData['@type']);
        $this->assertEquals('ConvertOKit', $structuredData['name']);
    }

    public function test_get_home_structured_data_with_services()
    {
        $services = [
            [
                'title' => 'Meta Ads Management',
                'description' => 'Professional Meta advertising',
                'category' => 'Meta Advertising'
            ],
            [
                'title' => 'Facebook Pixel Setup',
                'description' => 'Professional pixel implementation',
                'category' => 'Tracking & Analytics'
            ]
        ];

        $structuredData = $this->seoService->getHomeStructuredData($services);

        $this->assertArrayHasKey('hasOfferCatalog', $structuredData);
        $this->assertEquals('OfferCatalog', $structuredData['hasOfferCatalog']['@type']);
        $this->assertCount(2, $structuredData['hasOfferCatalog']['itemListElement']);
    }

    public function test_get_service_structured_data()
    {
        $service = Service::factory()->make([
            'title' => 'Test Service',
            'description' => 'Test service description',
            'category' => 'Test Category',
            'price_range' => '$100 - $500',
            'slug' => 'test-service'
        ]);

        $structuredData = $this->seoService->getServiceStructuredData($service);

        $this->assertArrayHasKey('@context', $structuredData);
        $this->assertArrayHasKey('@type', $structuredData);
        $this->assertArrayHasKey('name', $structuredData);
        $this->assertArrayHasKey('description', $structuredData);
        $this->assertArrayHasKey('provider', $structuredData);
        $this->assertArrayHasKey('category', $structuredData);
        $this->assertArrayHasKey('offers', $structuredData);

        $this->assertEquals('https://schema.org', $structuredData['@context']);
        $this->assertEquals('Service', $structuredData['@type']);
        $this->assertEquals('Test Service', $structuredData['name']);
        $this->assertEquals('Test service description', $structuredData['description']);
        $this->assertEquals('Test Category', $structuredData['category']);
    }

    public function test_get_blog_post_structured_data()
    {
        $category = Category::factory()->create(['name' => 'Test Category']);
        $user = User::factory()->create(['name' => 'John Doe']);

        $post = BlogPost::factory()->make([
            'title' => 'Test Blog Post',
            'excerpt' => 'Test excerpt',
            'content' => '<p>Test content</p>',
            'slug' => 'test-blog-post',
            'published_at' => now(),
            'updated_at' => now(),
            'featured_image' => 'test-image.jpg'
        ]);

        $post->category = $category;
        $post->author = $user;

        $structuredData = $this->seoService->getBlogPostStructuredData($post);

        $this->assertArrayHasKey('@context', $structuredData);
        $this->assertArrayHasKey('@type', $structuredData);
        $this->assertArrayHasKey('headline', $structuredData);
        $this->assertArrayHasKey('description', $structuredData);
        $this->assertArrayHasKey('articleBody', $structuredData);
        $this->assertArrayHasKey('author', $structuredData);
        $this->assertArrayHasKey('publisher', $structuredData);
        $this->assertArrayHasKey('image', $structuredData);
        $this->assertArrayHasKey('articleSection', $structuredData);

        $this->assertEquals('https://schema.org', $structuredData['@context']);
        $this->assertEquals('BlogPosting', $structuredData['@type']);
        $this->assertEquals('Test Blog Post', $structuredData['headline']);
        $this->assertEquals('Test excerpt', $structuredData['description']);
        $this->assertEquals('Test content', $structuredData['articleBody']);
        $this->assertEquals('John Doe', $structuredData['author']['name']);
        $this->assertEquals('Test Category', $structuredData['articleSection']);
    }

    public function test_get_team_structured_data()
    {
        $teamMembers = [
            [
                'name' => 'John Doe',
                'position' => 'Senior Developer',
                'bio' => 'Experienced developer'
            ],
            [
                'name' => 'Jane Smith',
                'position' => 'Marketing Manager',
                'bio' => 'Marketing expert'
            ]
        ];

        $structuredData = $this->seoService->getTeamStructuredData($teamMembers);

        $this->assertArrayHasKey('@context', $structuredData);
        $this->assertArrayHasKey('@type', $structuredData);
        $this->assertArrayHasKey('name', $structuredData);
        $this->assertArrayHasKey('employee', $structuredData);

        $this->assertEquals('https://schema.org', $structuredData['@context']);
        $this->assertEquals('Organization', $structuredData['@type']);
        $this->assertEquals('ConvertOKit', $structuredData['name']);
        $this->assertCount(2, $structuredData['employee']);

        $this->assertEquals('John Doe', $structuredData['employee'][0]['name']);
        $this->assertEquals('Senior Developer', $structuredData['employee'][0]['jobTitle']);
    }

    public function test_get_breadcrumb_structured_data()
    {
        $breadcrumbs = [
            ['name' => 'Home', 'url' => 'https://example.com/'],
            ['name' => 'Services', 'url' => 'https://example.com/services'],
            ['name' => 'Meta Ads', 'url' => 'https://example.com/services/meta-ads']
        ];

        $structuredData = $this->seoService->getBreadcrumbStructuredData($breadcrumbs);

        $this->assertArrayHasKey('@context', $structuredData);
        $this->assertArrayHasKey('@type', $structuredData);
        $this->assertArrayHasKey('itemListElement', $structuredData);

        $this->assertEquals('https://schema.org', $structuredData['@context']);
        $this->assertEquals('BreadcrumbList', $structuredData['@type']);
        $this->assertCount(3, $structuredData['itemListElement']);

        $this->assertEquals(1, $structuredData['itemListElement'][0]['position']);
        $this->assertEquals('Home', $structuredData['itemListElement'][0]['name']);
        $this->assertEquals('https://example.com/', $structuredData['itemListElement'][0]['item']);

        $this->assertEquals(3, $structuredData['itemListElement'][2]['position']);
        $this->assertEquals('Meta Ads', $structuredData['itemListElement'][2]['name']);
    }

    public function test_get_faq_structured_data()
    {
        $faqs = [
            [
                'question' => 'What is Meta advertising?',
                'answer' => 'Meta advertising refers to advertising on Facebook and Instagram platforms.'
            ],
            [
                'question' => 'How much does it cost?',
                'answer' => 'Pricing varies based on your needs and budget.'
            ]
        ];

        $structuredData = $this->seoService->getFaqStructuredData($faqs);

        $this->assertArrayHasKey('@context', $structuredData);
        $this->assertArrayHasKey('@type', $structuredData);
        $this->assertArrayHasKey('mainEntity', $structuredData);

        $this->assertEquals('https://schema.org', $structuredData['@context']);
        $this->assertEquals('FAQPage', $structuredData['@type']);
        $this->assertCount(2, $structuredData['mainEntity']);

        $this->assertEquals('Question', $structuredData['mainEntity'][0]['@type']);
        $this->assertEquals('What is Meta advertising?', $structuredData['mainEntity'][0]['name']);
        $this->assertEquals('Answer', $structuredData['mainEntity'][0]['acceptedAnswer']['@type']);
    }

    public function test_get_robots_tag()
    {
        $this->assertEquals('index, follow', $this->seoService->getRobotsTag());
        $this->assertEquals('index, nofollow', $this->seoService->getRobotsTag(true, false));
        $this->assertEquals('noindex, follow', $this->seoService->getRobotsTag(false, true));
        $this->assertEquals('noindex, nofollow', $this->seoService->getRobotsTag(false, false));
    }
}
