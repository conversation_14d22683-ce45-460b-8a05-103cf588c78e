<?php

namespace Tests\Unit;

use App\Services\BlogService;
use App\Models\BlogPost;
use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BlogServiceTest extends TestCase
{
    use RefreshDatabase;

    private BlogService $blogService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->blogService = new BlogService();
    }

    public function test_get_published_posts_returns_only_published_posts()
    {
        $category = Category::factory()->create();
        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Published Post',
            'excerpt' => 'Published post excerpt',
            'content' => 'Published post content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);
        BlogPost::factory()->create([
            'title' => 'Draft Post',
            'excerpt' => 'Draft post excerpt',
            'content' => 'Draft post content',
            'status' => 'draft',
            'published_at' => null,
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        $posts = $this->blogService->getPublishedPosts();

        $this->assertCount(1, $posts);
        $this->assertEquals('Published Post', $posts->first()->title);
    }

    public function test_get_published_posts_with_search_filter()
    {
        $category = Category::factory()->create();
        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Meta Ads Best Practices',
            'excerpt' => 'Learn about Meta advertising',
            'content' => 'Meta advertising content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);
        BlogPost::factory()->create([
            'title' => 'Google Analytics Guide',
            'excerpt' => 'Learn about Google Analytics',
            'content' => 'Google Analytics content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        $posts = $this->blogService->getPublishedPosts(['search' => 'Meta']);

        $this->assertCount(1, $posts);
        $this->assertEquals('Meta Ads Best Practices', $posts->first()->title);
    }

    public function test_get_published_posts_with_category_filter()
    {
        $category1 = Category::factory()->create(['name' => 'Meta Advertising']);
        $category2 = Category::factory()->create(['name' => 'Analytics']);
        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Meta Post',
            'excerpt' => 'Meta post excerpt',
            'content' => 'Meta post content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category1->id,
            'author_id' => $user->id
        ]);
        BlogPost::factory()->create([
            'title' => 'Analytics Post',
            'excerpt' => 'Analytics post excerpt',
            'content' => 'Analytics post content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category2->id,
            'author_id' => $user->id
        ]);

        $posts = $this->blogService->getPublishedPosts(['category_id' => $category1->id]);

        $this->assertCount(1, $posts);
        $this->assertEquals('Meta Post', $posts->first()->title);
    }

    public function test_get_published_posts_with_sorting()
    {
        $category = Category::factory()->create();
        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Z Post',
            'excerpt' => 'Z post excerpt',
            'content' => 'Z post content',
            'status' => 'published',
            'published_at' => now()->subDays(1),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);
        BlogPost::factory()->create([
            'title' => 'A Post',
            'excerpt' => 'A post excerpt',
            'content' => 'A post content',
            'status' => 'published',
            'published_at' => now()->subDays(2),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);
        BlogPost::factory()->create([
            'title' => 'M Post',
            'excerpt' => 'M post excerpt',
            'content' => 'M post content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        // Test sort by published_at descending (default)
        $posts = $this->blogService->getPublishedPosts([
            'sort_by' => 'published_at',
            'sort_direction' => 'desc'
        ]);

        $this->assertEquals('M Post', $posts->first()->title);
        $this->assertEquals('A Post', $posts->last()->title);

        // Test sort by title ascending
        $posts = $this->blogService->getPublishedPosts([
            'sort_by' => 'title',
            'sort_direction' => 'asc'
        ]);

        $this->assertEquals('A Post', $posts->first()->title);
        $this->assertEquals('Z Post', $posts->last()->title);
    }

    public function test_get_published_posts_with_pagination()
    {
        $category = Category::factory()->create();
        $user = User::factory()->create();

        for ($i = 1; $i <= 15; $i++) {
            BlogPost::factory()->create([
                'title' => "Test Post {$i}",
                'excerpt' => "Test excerpt {$i}",
                'content' => "Test content {$i}",
                'status' => 'published',
                'published_at' => now(),
                'category_id' => $category->id,
                'author_id' => $user->id
            ]);
        }

        $posts = $this->blogService->getPublishedPosts([
            'paginate' => true,
            'limit' => 10
        ]);

        $this->assertInstanceOf(\Illuminate\Pagination\LengthAwarePaginator::class, $posts);
        $this->assertEquals(10, $posts->perPage());
        $this->assertEquals(15, $posts->total());
        $this->assertEquals(2, $posts->lastPage());
    }

    public function test_find_by_slug_returns_correct_post()
    {
        $category = Category::factory()->create();
        $user = User::factory()->create();

        $post = BlogPost::factory()->create([
            'title' => 'Test Post',
            'slug' => 'test-post',
            'excerpt' => 'Test post excerpt',
            'content' => 'Test post content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        $foundPost = $this->blogService->findBySlug('test-post');

        $this->assertNotNull($foundPost);
        $this->assertEquals('Test Post', $foundPost->title);
        $this->assertEquals('test-post', $foundPost->slug);
    }

    public function test_find_by_slug_returns_null_for_draft_post()
    {
        $category = Category::factory()->create();
        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Draft Post',
            'slug' => 'draft-post',
            'excerpt' => 'Draft post excerpt',
            'content' => 'Draft post content',
            'status' => 'draft',
            'published_at' => null,
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        $foundPost = $this->blogService->findBySlug('draft-post');

        $this->assertNull($foundPost);
    }

    public function test_increment_views_increases_view_count()
    {
        $category = Category::factory()->create();
        $user = User::factory()->create();

        $post = BlogPost::factory()->create([
            'title' => 'Test Post',
            'excerpt' => 'Test post excerpt',
            'content' => 'Test post content',
            'status' => 'published',
            'published_at' => now(),
            'views_count' => 5,
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        $this->blogService->incrementViews($post->id);

        $post->refresh();
        $this->assertEquals(6, $post->views_count);
    }

    public function test_increment_views_handles_null_views_count()
    {
        $category = Category::factory()->create();
        $user = User::factory()->create();

        $post = BlogPost::factory()->create([
            'title' => 'Test Post',
            'excerpt' => 'Test post excerpt',
            'content' => 'Test post content',
            'status' => 'published',
            'published_at' => now(),
            'views_count' => 0,
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        $this->blogService->incrementViews($post->id);

        $post->refresh();
        $this->assertEquals(1, $post->views_count);
    }

    public function test_get_active_categories_returns_only_active_categories()
    {
        Category::factory()->create(['name' => 'Active Category', 'is_active' => true]);
        Category::factory()->create(['name' => 'Inactive Category', 'is_active' => false]);

        $categories = $this->blogService->getActiveCategories();

        $this->assertCount(1, $categories);
        $this->assertEquals('Active Category', $categories->first()->name);
    }

    public function test_get_active_categories_with_search()
    {
        Category::factory()->create(['name' => 'Meta Advertising', 'is_active' => true]);
        Category::factory()->create(['name' => 'Google Analytics', 'is_active' => true]);

        $categories = $this->blogService->getActiveCategories(['search' => 'Meta']);

        $this->assertCount(1, $categories);
        $this->assertEquals('Meta Advertising', $categories->first()->name);
    }

    public function test_get_active_categories_sorted_by_sort_order()
    {
        Category::factory()->create([
            'name' => 'Third Category',
            'sort_order' => 3,
            'is_active' => true
        ]);
        Category::factory()->create([
            'name' => 'First Category',
            'sort_order' => 1,
            'is_active' => true
        ]);
        Category::factory()->create([
            'name' => 'Second Category',
            'sort_order' => 2,
            'is_active' => true
        ]);

        $categories = $this->blogService->getActiveCategories();

        $this->assertEquals('First Category', $categories->first()->name);
        $this->assertEquals('Third Category', $categories->last()->name);
    }

    public function test_get_published_posts_with_multiple_filters()
    {
        $category1 = Category::factory()->create(['name' => 'Meta Advertising']);
        $category2 = Category::factory()->create(['name' => 'Analytics']);
        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Meta Ads Best Practices',
            'excerpt' => 'Learn about Meta advertising',
            'content' => 'Meta advertising content',
            'status' => 'published',
            'published_at' => now()->subDays(1),
            'category_id' => $category1->id,
            'author_id' => $user->id
        ]);
        BlogPost::factory()->create([
            'title' => 'Meta Pixel Setup',
            'excerpt' => 'Learn about Meta pixel',
            'content' => 'Meta pixel content',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category1->id,
            'author_id' => $user->id
        ]);
        BlogPost::factory()->create([
            'title' => 'Google Analytics Guide',
            'excerpt' => 'Learn about Google Analytics',
            'content' => 'Google Analytics content',
            'status' => 'published',
            'published_at' => now()->subDays(2),
            'category_id' => $category2->id,
            'author_id' => $user->id
        ]);

        $posts = $this->blogService->getPublishedPosts([
            'search' => 'Meta',
            'category_id' => $category1->id,
            'sort_by' => 'published_at',
            'sort_direction' => 'desc',
            'limit' => 5
        ]);

        $this->assertCount(2, $posts);
        $this->assertEquals('Meta Pixel Setup', $posts->first()->title);
        $this->assertEquals('Meta Ads Best Practices', $posts->last()->title);
    }

    public function test_get_published_posts_search_in_content()
    {
        $category = Category::factory()->create();
        $user = User::factory()->create();

        BlogPost::factory()->create([
            'title' => 'Post One',
            'excerpt' => 'Regular excerpt',
            'content' => 'This post contains Meta advertising information',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);
        BlogPost::factory()->create([
            'title' => 'Post Two',
            'excerpt' => 'Regular excerpt',
            'content' => 'This post contains Google Analytics information',
            'status' => 'published',
            'published_at' => now(),
            'category_id' => $category->id,
            'author_id' => $user->id
        ]);

        $posts = $this->blogService->getPublishedPosts(['search' => 'Meta']);

        $this->assertCount(1, $posts);
        $this->assertEquals('Post One', $posts->first()->title);
    }
}
