import { type BreadcrumbItem, type SharedData } from '@/types';
import { Transition } from '@headlessui/react';
import { Form, Head, Link, usePage } from '@inertiajs/react';

import DeleteUser from '@/components/delete-user';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import AppLayout from '@/layouts/app-layout';
import SettingsLayout from '@/layouts/settings/layout';
import { User, Phone, Building, Camera } from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Profile settings',
        href: '/settings/profile',
    },
];

export default function Profile({ mustVerifyEmail, status }: { mustVerifyEmail: boolean; status?: string }) {
    const { auth } = usePage<SharedData>().props;

    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const formatRole = (role: string) => {
        return role
            .replace('_', ' ')
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Profile settings" />

            <SettingsLayout>
                <div className="space-y-6">
                    {/* Profile Header */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Profile Information</CardTitle>
                            <CardDescription>
                                Update your personal information and profile settings.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-6">
                                <div className="relative">
                                    <Avatar className="h-20 w-20">
                                        <AvatarImage src={auth.user.avatar} alt={auth.user.name} />
                                        <AvatarFallback className="text-lg">
                                            {getInitials(auth.user.name)}
                                        </AvatarFallback>
                                    </Avatar>
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
                                    >
                                        <Camera className="h-4 w-4" />
                                    </Button>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold">{auth.user.name}</h3>
                                    <p className="text-sm text-muted-foreground">{auth.user.email}</p>
                                    <p className="text-sm text-muted-foreground">
                                        {formatRole(auth.user.role)} Account
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Profile Form */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Personal Details</CardTitle>
                            <CardDescription>
                                Update your personal information and contact details.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Form
                                method="patch"
                                action={route('profile.update')}
                                options={{
                                    preserveScroll: true,
                                }}
                                className="space-y-6"
                            >
                                {({ processing, recentlySuccessful, errors }) => (
                                    <>
                                        <div className="grid gap-6 md:grid-cols-2">
                                            <div className="grid gap-2">
                                                <Label htmlFor="name">
                                                    <User className="inline mr-2 h-4 w-4" />
                                                    Full Name
                                                </Label>
                                                <Input
                                                    id="name"
                                                    className="mt-1 block w-full"
                                                    defaultValue={auth.user.name}
                                                    name="name"
                                                    required
                                                    autoComplete="name"
                                                    placeholder="Enter your full name"
                                                />
                                                <InputError className="mt-2" message={errors.name} />
                                            </div>

                                            <div className="grid gap-2">
                                                <Label htmlFor="email">Email Address</Label>
                                                <Input
                                                    id="email"
                                                    type="email"
                                                    className="mt-1 block w-full"
                                                    defaultValue={auth.user.email}
                                                    name="email"
                                                    required
                                                    autoComplete="username"
                                                    placeholder="Enter your email address"
                                                />
                                                <InputError className="mt-2" message={errors.email} />
                                            </div>
                                        </div>

                                        <div className="grid gap-6 md:grid-cols-2">
                                            <div className="grid gap-2">
                                                <Label htmlFor="phone">
                                                    <Phone className="inline mr-2 h-4 w-4" />
                                                    Phone Number
                                                </Label>
                                                <Input
                                                    id="phone"
                                                    type="tel"
                                                    className="mt-1 block w-full"
                                                    defaultValue={auth.user.phone || ''}
                                                    name="phone"
                                                    autoComplete="tel"
                                                    placeholder="Enter your phone number"
                                                />
                                                <InputError className="mt-2" message={errors.phone} />
                                            </div>

                                            <div className="grid gap-2">
                                                <Label htmlFor="company">
                                                    <Building className="inline mr-2 h-4 w-4" />
                                                    Company
                                                </Label>
                                                <Input
                                                    id="company"
                                                    className="mt-1 block w-full"
                                                    defaultValue={auth.user.company || ''}
                                                    name="company"
                                                    autoComplete="organization"
                                                    placeholder="Enter your company name"
                                                />
                                                <InputError className="mt-2" message={errors.company} />
                                            </div>
                                        </div>

                                        {mustVerifyEmail && auth.user.email_verified_at === null && (
                                            <div className="col-span-2">
                                                <div className="rounded-md bg-yellow-50 p-4 border border-yellow-200">
                                                    <p className="text-sm text-yellow-800">
                                                        Your email address is unverified.{' '}
                                                        <Link
                                                            href={route('verification.send')}
                                                            method="post"
                                                            as="button"
                                                            className="font-medium underline hover:no-underline"
                                                        >
                                                            Click here to resend the verification email.
                                                        </Link>
                                                    </p>

                                                    {status === 'verification-link-sent' && (
                                                        <div className="mt-2 text-sm font-medium text-green-600">
                                                            A new verification link has been sent to your email address.
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                        <div className="flex items-center justify-between pt-4">
                                            <div className="flex items-center gap-4">
                                                <Button disabled={processing}>
                                                    {processing ? 'Saving...' : 'Save Changes'}
                                                </Button>

                                                <Transition
                                                    show={recentlySuccessful}
                                                    enter="transition ease-in-out"
                                                    enterFrom="opacity-0"
                                                    leave="transition ease-in-out"
                                                    leaveTo="opacity-0"
                                                >
                                                    <p className="text-sm text-green-600 font-medium">Profile updated successfully!</p>
                                                </Transition>
                                            </div>
                                        </div>
                                    </>
                                )}
                            </Form>
                        </CardContent>
                    </Card>

                    {/* Account Security */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Account Security</CardTitle>
                            <CardDescription>
                                Manage your account security and password settings.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="text-sm font-medium">Password</h4>
                                        <p className="text-sm text-muted-foreground">
                                            Last updated: {auth.user.updated_at ? new Date(auth.user.updated_at).toLocaleDateString() : 'Never'}
                                        </p>
                                    </div>
                                    <Button variant="outline" asChild>
                                        <Link href="/settings/password">Change Password</Link>
                                    </Button>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="text-sm font-medium">Email Verification</h4>
                                        <p className="text-sm text-muted-foreground">
                                            {auth.user.email_verified_at ? 'Verified' : 'Not verified'}
                                        </p>
                                    </div>
                                    {auth.user.email_verified_at ? (
                                        <div className="text-sm text-green-600 font-medium">✓ Verified</div>
                                    ) : (
                                        <Button variant="outline" size="sm">
                                            Verify Email
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <DeleteUser />
                </div>
            </SettingsLayout>
        </AppLayout>
    );
}
