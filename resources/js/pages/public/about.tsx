import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import PublicLayout from '@/layouts/public-layout';
import { Head, Link } from '@inertiajs/react';
import { ArrowRight, Award, Target, Users, Zap, CheckCircle, Star } from 'lucide-react';

interface AboutProps {
    meta: {
        title: string;
        description: string;
        keywords?: string;
        canonical: string;
    };
}

export default function About({ meta }: AboutProps) {
    const achievements = [
        { icon: Users, value: '500+', label: 'Happy Clients' },
        { icon: Target, value: '300%', label: 'Average ROI Increase' },
        { icon: Zap, value: '99%', label: 'Tracking Accuracy' },
        { icon: Award, value: '5+', label: 'Years Experience' },
    ];

    const values = [
        {
            title: 'Performance-Driven',
            description: 'Every strategy we implement is designed to deliver measurable results and maximize your return on investment.',
            icon: Target,
        },
        {
            title: 'Transparency',
            description: 'We believe in clear communication and provide detailed reporting on all campaigns and tracking implementations.',
            icon: CheckCircle,
        },
        {
            title: 'Innovation',
            description: 'We stay ahead of industry trends and continuously adopt new technologies to give our clients a competitive edge.',
            icon: Zap,
        },
        {
            title: 'Partnership',
            description: 'We work as an extension of your team, understanding your business goals and aligning our strategies accordingly.',
            icon: Users,
        },
    ];

    const certifications = [
        'Facebook Blueprint Certified',
        'Google Analytics Certified',
        'Google Ads Certified',
        'Facebook Marketing Partner',
        'Google Tag Manager Certified',
    ];

    return (
        <PublicLayout>
            <Head
                title={meta.title}
                description={meta.description}
            >
                <meta name="keywords" content={meta.keywords} />
                <meta property="og:title" content={meta.title} />
                <meta property="og:description" content={meta.description} />
                <link rel="canonical" href={meta.canonical} />
            </Head>

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <Badge variant="secondary" className="mb-6 px-4 py-2">
                            About ConvertOKit
                        </Badge>
                        <h1 className="text-4xl font-bold mb-6 sm:text-5xl">
                            Experts in Meta Ads & Web Analytics
                        </h1>
                        <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                            We're a dedicated team of digital marketing specialists who help businesses 
                            maximize their online advertising ROI through expert Meta advertising management 
                            and advanced web analytics implementation.
                        </p>
                        <Button size="lg" asChild>
                            <Link href="/contact">
                                Get Started Today
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>

            {/* Stats Section */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                        {achievements.map((achievement, index) => (
                            <div key={index} className="text-center">
                                <div className="flex justify-center mb-4">
                                    <achievement.icon className="h-12 w-12 text-primary" />
                                </div>
                                <div className="text-3xl font-bold text-foreground mb-2">
                                    {achievement.value}
                                </div>
                                <p className="text-sm text-muted-foreground font-medium">
                                    {achievement.label}
                                </p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Our Story Section */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Our Story</h2>
                            <p className="text-lg text-muted-foreground">
                                How we became the go-to experts for Meta advertising and web analytics
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                            <div>
                                <h3 className="text-2xl font-semibold mb-4">Founded on Performance</h3>
                                <p className="text-muted-foreground mb-6 leading-relaxed">
                                    ConvertOKit was founded with a simple mission: to help businesses achieve 
                                    exceptional results from their digital marketing investments. We recognized 
                                    that many companies were struggling with ineffective ad campaigns and 
                                    inaccurate tracking, leading to wasted budgets and missed opportunities.
                                </p>
                                <p className="text-muted-foreground mb-6 leading-relaxed">
                                    Our team combines deep expertise in Meta advertising platforms with 
                                    advanced knowledge of web analytics and conversion tracking. This unique 
                                    combination allows us to not only run high-performing campaigns but also 
                                    ensure that every conversion is accurately tracked and attributed.
                                </p>
                                <p className="text-muted-foreground leading-relaxed">
                                    Today, we're proud to have helped over 500 businesses across various 
                                    industries achieve an average ROI increase of 300% through our proven 
                                    strategies and meticulous attention to data accuracy.
                                </p>
                            </div>
                            <div className="space-y-6">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <Star className="h-5 w-5 text-yellow-500 mr-2" />
                                            Our Mission
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-muted-foreground">
                                            To empower businesses with data-driven digital marketing strategies 
                                            that deliver measurable results and sustainable growth.
                                        </p>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <Target className="h-5 w-5 text-primary mr-2" />
                                            Our Vision
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-muted-foreground">
                                            To be the leading authority in Meta advertising and web analytics, 
                                            setting the standard for performance and transparency in digital marketing.
                                        </p>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Values Section */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Our Values</h2>
                            <p className="text-lg text-muted-foreground">
                                The principles that guide everything we do
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {values.map((value, index) => (
                                <Card key={index} className="border-0 shadow-md">
                                    <CardHeader>
                                        <CardTitle className="flex items-center">
                                            <value.icon className="h-6 w-6 text-primary mr-3" />
                                            {value.title}
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-muted-foreground leading-relaxed">
                                            {value.description}
                                        </p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Certifications Section */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <h2 className="text-3xl font-bold mb-4">Certifications & Expertise</h2>
                        <p className="text-lg text-muted-foreground mb-8">
                            Our team holds industry-leading certifications to ensure we deliver the highest quality service
                        </p>
                        
                        <div className="flex flex-wrap justify-center gap-3 mb-8">
                            {certifications.map((cert, index) => (
                                <Badge key={index} variant="secondary" className="px-4 py-2">
                                    {cert}
                                </Badge>
                            ))}
                        </div>
                        
                        <p className="text-muted-foreground mb-8">
                            We continuously invest in training and certification to stay ahead of platform updates 
                            and industry best practices, ensuring our clients always benefit from the latest strategies and techniques.
                        </p>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Ready to Work Together?</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Let's discuss how our expertise can help you achieve your digital marketing goals
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="secondary" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
                            <Link href="/team">
                                Meet Our Team
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
