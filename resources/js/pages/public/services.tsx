import { ServiceGrid } from '@/components/service-card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import SeoHead from '@/components/seo-head';
import PublicLayout from '@/layouts/public-layout';
import { type Service } from '@/types';
import { Link, router } from '@inertiajs/react';
import { ArrowRight, CheckCircle, Target, TrendingUp, Zap, Search, X } from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';
import { SELECT_ALL_CATEGORIES } from '@/lib/utils';

interface ServicesProps {
    services: Service[];
    categories: string[];
    filters: {
        search?: string;
        category?: string;
        sort_by: string;
        sort_direction: string;
    };
    meta: {
        title: string;
        description: string;
        keywords?: string;
        canonical: string;
        og_title?: string;
        og_description?: string;
        og_type?: string;
        og_url?: string;
        og_site_name?: string;
        og_image?: string;
        twitter_card?: string;
        twitter_title?: string;
        twitter_description?: string;
        twitter_image?: string;
    };
}

export default function Services({ services, categories, filters, meta }: ServicesProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedCategory, setSelectedCategory] = useState(filters.category || SELECT_ALL_CATEGORIES);
    const [sortBy, setSortBy] = useState(filters.sort_by || 'sort_order');
    const [sortDirection, setSortDirection] = useState(filters.sort_direction || 'asc');

    // Handle search and filter changes
    const handleSearch = useCallback(() => {
        const params = new URLSearchParams();
        if (searchTerm) params.set('search', searchTerm);
        if (selectedCategory && selectedCategory !== SELECT_ALL_CATEGORIES) params.set('category', selectedCategory);
        if (sortBy !== 'sort_order') params.set('sort_by', sortBy);
        if (sortDirection !== 'asc') params.set('sort_direction', sortDirection);

        router.get('/services', Object.fromEntries(params), {
            preserveState: true,
            preserveScroll: true,
        });
    }, [searchTerm, selectedCategory, sortBy, sortDirection]);

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedCategory(SELECT_ALL_CATEGORIES);
        setSortBy('sort_order');
        setSortDirection('asc');
        router.get('/services', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    // Auto-search on input change with debounce
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchTerm !== (filters.search || '')) {
                handleSearch();
            }
        }, 500);
        return () => clearTimeout(timeoutId);
    }, [searchTerm, filters.search, handleSearch]);

    // Group services by category
    const servicesByCategory = services.reduce((acc, service) => {
        const category = service.category || 'Other';
        if (!acc[category]) {
            acc[category] = [];
        }
        acc[category].push(service);
        return acc;
    }, {} as Record<string, Service[]>);

    const benefits = [
        {
            icon: Target,
            title: 'Proven Results',
            description: 'Our strategies have helped clients achieve an average 300% increase in ROI'
        },
        {
            icon: Zap,
            title: 'Advanced Tracking',
            description: '99% tracking accuracy with server-side implementation and data validation'
        },
        {
            icon: TrendingUp,
            title: 'Continuous Optimization',
            description: 'Ongoing monitoring and optimization to ensure peak performance'
        },
        {
            icon: CheckCircle,
            title: 'Full Transparency',
            description: 'Detailed reporting and clear communication throughout the process'
        }
    ];

    const hasActiveFilters = searchTerm || (selectedCategory && selectedCategory !== SELECT_ALL_CATEGORIES) || sortBy !== 'sort_order' || sortDirection !== 'asc';

    return (
        <PublicLayout>
            <SeoHead meta={meta} />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <Badge variant="secondary" className="mb-6 px-4 py-2">
                            Our Services
                        </Badge>
                        <h1 className="text-4xl font-bold mb-6 sm:text-5xl">
                            Expert Meta Ads & Analytics Services
                        </h1>
                        <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                            Comprehensive digital marketing solutions designed to maximize your ROI 
                            and provide accurate tracking of your marketing performance.
                        </p>
                        <Button size="lg" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>

            {/* Benefits Section */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Why Choose ConvertOKit?</h2>
                            <p className="text-lg text-muted-foreground">
                                We deliver results through expertise, transparency, and proven methodologies
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {benefits.map((benefit, index) => (
                                <Card key={index} className="text-center border-0 shadow-md">
                                    <CardHeader>
                                        <div className="flex justify-center mb-4">
                                            <benefit.icon className="h-12 w-12 text-primary" />
                                        </div>
                                        <CardTitle className="text-lg">{benefit.title}</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <p className="text-sm text-muted-foreground leading-relaxed">
                                            {benefit.description}
                                        </p>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Search and Filter */}
            <section className="py-8 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="flex flex-col md:flex-row gap-4 items-center">
                            {/* Search Input */}
                            <div className="relative flex-1">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                <Input
                                    type="text"
                                    placeholder="Search services..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>

                            {/* Category Filter */}
                            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                                <SelectTrigger className="w-full md:w-48">
                                    <SelectValue placeholder="All Categories" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value={SELECT_ALL_CATEGORIES}>All Categories</SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem key={category} value={category}>
                                            {category}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            {/* Sort Options */}
                            <Select value={`${sortBy}-${sortDirection}`} onValueChange={(value) => {
                                const [field, direction] = value.split('-');
                                setSortBy(field);
                                setSortDirection(direction);
                                setTimeout(handleSearch, 100);
                            }}>
                                <SelectTrigger className="w-full md:w-48">
                                    <SelectValue placeholder="Sort by" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="sort_order-asc">Default Order</SelectItem>
                                    <SelectItem value="title-asc">Name (A-Z)</SelectItem>
                                    <SelectItem value="title-desc">Name (Z-A)</SelectItem>
                                    <SelectItem value="category-asc">Category</SelectItem>
                                </SelectContent>
                            </Select>

                            {/* Clear Filters */}
                            {hasActiveFilters && (
                                <Button variant="outline" onClick={clearFilters} className="whitespace-nowrap">
                                    <X className="h-4 w-4 mr-2" />
                                    Clear
                                </Button>
                            )}
                        </div>

                        {/* Active Filters Display */}
                        {hasActiveFilters && (
                            <div className="mt-4 flex flex-wrap gap-2">
                                {searchTerm && (
                                    <Badge variant="secondary">
                                        Search: "{searchTerm}"
                                    </Badge>
                                )}
                                {selectedCategory && selectedCategory !== SELECT_ALL_CATEGORIES && (
                                    <Badge variant="secondary">
                                        Category: {selectedCategory}
                                    </Badge>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </section>

            {/* Services Results */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        {services.length === 0 ? (
                            <div className="text-center py-12">
                                <h3 className="text-2xl font-semibold mb-4">No services found</h3>
                                <p className="text-muted-foreground mb-6">
                                    Try adjusting your search criteria or browse all services.
                                </p>
                                <Button onClick={clearFilters}>
                                    View All Services
                                </Button>
                            </div>
                        ) : (
                            <>
                                <div className="text-center mb-12">
                                    <h2 className="text-3xl font-bold mb-4">
                                        {hasActiveFilters ? 'Search Results' : 'Our Services'}
                                    </h2>
                                    <p className="text-lg text-muted-foreground">
                                        {services.length} service{services.length !== 1 ? 's' : ''} found
                                    </p>
                                </div>
                                {hasActiveFilters ? (
                                    // Show filtered results as a simple grid
                                    <ServiceGrid services={services} />
                                ) : (
                                    // Show services grouped by category
                                    Object.entries(servicesByCategory).map(([category, categoryServices], index) => (
                                        <div key={category} className={index > 0 ? 'mt-16' : ''}>
                                            <div className="text-center mb-12">
                                                <h3 className="text-2xl font-bold mb-4">{category}</h3>
                                                <p className="text-lg text-muted-foreground">
                                                    {category === 'Meta Advertising' && 'Professional Facebook and Instagram advertising management'}
                                                    {category === 'Tracking & Analytics' && 'Advanced tracking implementation and analytics setup'}
                                                    {category === 'Consulting' && 'Strategic guidance and optimization recommendations'}
                                                    {category === 'Other' && 'Additional services to support your digital marketing goals'}
                                                </p>
                                            </div>
                                            <ServiceGrid services={categoryServices} />
                                        </div>
                                    ))
                                )}
                            </>
                        )}
                    </div>
                </div>
            </section>

            {/* Process Section */}
            <section className="py-16 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold mb-4">Our Process</h2>
                            <p className="text-lg text-muted-foreground">
                                A proven methodology that delivers consistent results
                            </p>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div className="text-center">
                                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                                    1
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Discovery & Analysis</h3>
                                <p className="text-muted-foreground">
                                    We analyze your current setup, understand your goals, and identify opportunities for improvement.
                                </p>
                            </div>
                            <div className="text-center">
                                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                                    2
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Strategy & Implementation</h3>
                                <p className="text-muted-foreground">
                                    We develop a customized strategy and implement solutions using industry best practices.
                                </p>
                            </div>
                            <div className="text-center">
                                <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                                    3
                                </div>
                                <h3 className="text-xl font-semibold mb-2">Optimization & Reporting</h3>
                                <p className="text-muted-foreground">
                                    We continuously monitor performance and provide detailed reports with actionable insights.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Let's discuss your specific needs and create a customized solution for your business
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="secondary" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
                            <Link href="/contact">
                                Contact Us
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
