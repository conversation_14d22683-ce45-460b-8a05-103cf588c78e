import { TeamGrid } from '@/components/team-member-card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import PublicLayout from '@/layouts/public-layout';
import { type TeamMember } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowRight } from 'lucide-react';

interface TeamProps {
    teamMembers: TeamMember[];
    meta: {
        title: string;
        description: string;
        keywords?: string;
        canonical: string;
    };
}

export default function Team({ teamMembers, meta }: TeamProps) {
    return (
        <PublicLayout>
            <Head
                title={meta.title}
                description={meta.description}
            >
                <meta name="keywords" content={meta.keywords} />
                <meta property="og:title" content={meta.title} />
                <meta property="og:description" content={meta.description} />
                <link rel="canonical" href={meta.canonical} />
            </Head>

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <Badge variant="secondary" className="mb-6 px-4 py-2">
                            Our Team
                        </Badge>
                        <h1 className="text-4xl font-bold mb-6 sm:text-5xl">
                            Meet the Experts Behind ConvertOKit
                        </h1>
                        <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                            Our team of certified digital marketing specialists brings years of experience 
                            in Meta advertising, web analytics, and conversion optimization to help your business succeed.
                        </p>
                        <Button size="lg" asChild>
                            <Link href="/book-consultation">
                                Work With Our Team
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>

            {/* Team Members */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        {teamMembers.length > 0 ? (
                            <TeamGrid members={teamMembers} />
                        ) : (
                            <div className="text-center">
                                <p className="text-lg text-muted-foreground mb-8">
                                    Our team information will be available soon. In the meantime, 
                                    feel free to reach out to learn more about our expertise.
                                </p>
                                <Button asChild>
                                    <Link href="/contact">Contact Us</Link>
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Ready to Work With Our Team?</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Let's discuss your digital marketing goals and see how our expertise can help you achieve them
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" variant="secondary" asChild>
                            <Link href="/book-consultation">
                                Book Free Consultation
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Link>
                        </Button>
                        <Button size="lg" variant="outline" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary" asChild>
                            <Link href="/contact">
                                Get In Touch
                            </Link>
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
