import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Home from '../public/home';

// Mock all the components used in Home
vi.mock('@/components/hero-section', () => ({
  HeroSection: () => <div data-testid="hero-section">Hero Section</div>,
}));

vi.mock('@/components/service-card', () => ({
  ServiceGrid: ({ services }: { services: Array<{ id: number; title: string }> }) => (
    <div data-testid="service-grid">
      {services.map((service: { id: number; title: string }) => (
        <div key={service.id} data-testid={`service-${service.id}`}>
          {service.title}
        </div>
      ))}
    </div>
  ),
}));

vi.mock('@/components/team-member-card', () => ({
  TeamGrid: ({ members }: { members: Array<{ id: number; name: string }> }) => (
    <div data-testid="team-grid">
      {members.map((member: { id: number; name: string }) => (
        <div key={member.id} data-testid={`member-${member.id}`}>
          {member.name}
        </div>
      ))}
    </div>
  ),
}));

vi.mock('@/components/blog-post-card', () => ({
  BlogPostGrid: ({ posts }: { posts: Array<{ id: number; title: string }> }) => (
    <div data-testid="blog-grid">
      {posts.map((post: { id: number; title: string }) => (
        <div key={post.id} data-testid={`post-${post.id}`}>
          {post.title}
        </div>
      ))}
    </div>
  ),
}));

vi.mock('@/layouts/public-layout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="public-layout">{children}</div>,
}));

vi.mock('@inertiajs/react', () => ({
  Head: ({ title }: { title: string }) => <title>{title}</title>,
  Link: ({ children, href, ...props }: { children: React.ReactNode; href: string; [key: string]: unknown }) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}));

describe('Home Page', () => {
  const mockProps = {
    services: [
      {
        id: 1,
        title: 'Meta Ads Management',
        slug: 'meta-ads-management',
        description: 'Professional Meta advertising services',
        detailed_description: 'Detailed description',
        features: ['Feature 1', 'Feature 2'],
        price_range: '$1000-$2000',
        category_id: 1,
        category: { id: 1, name: 'Advertising', slug: 'advertising' },
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      }
    ],
    teamMembers: [
      {
        id: 1,
        name: 'John Doe',
        position: 'Marketing Director',
        bio: 'Experienced marketing professional',
        image: '/images/team/john.jpg',
        linkedin_url: 'https://linkedin.com/in/johndoe',
        twitter_url: 'https://twitter.com/johndoe',
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      }
    ],
    blogPosts: [
      {
        id: 1,
        title: 'Test Blog Post',
        slug: 'test-blog-post',
        excerpt: 'Test excerpt',
        content: 'Test content',
        featured_image: '/images/blog/test.jpg',
        published_at: '2024-01-01',
        category_id: 1,
        category: { id: 1, name: 'Marketing', slug: 'marketing' },
        author: { id: 1, name: 'Author Name', email: '<EMAIL>' },
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      }
    ],
    meta: {
      title: 'ConvertOKit - Digital Marketing Agency',
      description: 'Professional digital marketing services',
      canonical: 'https://convertokit.com'
    }
  };

  it('renders without crashing', () => {
    render(<Home {...mockProps} />);

    expect(screen.getByTestId('public-layout')).toBeInTheDocument();
    expect(screen.getByTestId('hero-section')).toBeInTheDocument();
  });

  it('renders hero section', () => {
    render(<Home {...mockProps} />);

    expect(screen.getByTestId('hero-section')).toBeInTheDocument();
  });

  it('renders about section', () => {
    render(<Home {...mockProps} />);

    expect(screen.getByText('About ConvertOKit')).toBeInTheDocument();
    expect(screen.getByText(/We're a team of digital marketing specialists/)).toBeInTheDocument();
  });

  it('renders services section with mock services', () => {
    render(<Home {...mockProps} />);

    expect(screen.getByText('Our Services')).toBeInTheDocument();
    expect(screen.getByTestId('service-grid')).toBeInTheDocument();

    // Check for mock services
    expect(screen.getByTestId('service-1')).toBeInTheDocument();
    expect(screen.getByText('Meta Ads Management')).toBeInTheDocument();
  });

  it('renders services section with provided services', () => {
    const customProps = {
      ...mockProps,
      services: [
        {
          id: 1,
          title: 'Custom Service',
          slug: 'custom-service',
          description: 'Custom description',
          detailed_description: '',
          features: [],
          price_range: '$1000',
          category_id: 1,
          category: { id: 1, name: 'Custom', slug: 'custom' },
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
        },
      ]
    };

    render(<Home {...customProps} />);

    expect(screen.getByText('Custom Service')).toBeInTheDocument();
  });

  it('renders team section when team members are provided', () => {
    render(<Home {...mockProps} />);

    expect(screen.getByText('Meet Our Team')).toBeInTheDocument();
    expect(screen.getByTestId('team-grid')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('does not render team section when no team members', () => {
    const propsWithoutTeam = { ...mockProps, teamMembers: [] };
    render(<Home {...propsWithoutTeam} />);

    expect(screen.queryByText('Meet Our Team')).not.toBeInTheDocument();
  });

  it('renders blog section when blog posts are provided', () => {
    render(<Home {...mockProps} />);

    expect(screen.getByText('Latest Insights')).toBeInTheDocument();
    expect(screen.getByTestId('blog-grid')).toBeInTheDocument();
    expect(screen.getByText('Test Blog Post')).toBeInTheDocument();
  });

  it('does not render blog section when no blog posts', () => {
    const propsWithoutBlog = { ...mockProps, blogPosts: [] };
    render(<Home {...propsWithoutBlog} />);

    expect(screen.queryByText('Latest Insights')).not.toBeInTheDocument();
  });

  it('renders CTA section', () => {
    render(<Home {...mockProps} />);

    expect(screen.getByText('Ready to Boost Your Digital Marketing?')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /Book Free Consultation/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /Get In Touch/i })).toBeInTheDocument();
  });

  it('has correct navigation links', () => {
    render(<Home {...mockProps} />);

    const consultationLink = screen.getByRole('link', { name: /Book Free Consultation/i });
    const contactLink = screen.getByRole('link', { name: /Get In Touch/i });
    const servicesLink = screen.getByRole('link', { name: /View All Services/i });

    expect(consultationLink).toHaveAttribute('href', '/book-consultation');
    expect(contactLink).toHaveAttribute('href', '/contact');
    expect(servicesLink).toHaveAttribute('href', '/services');
  });

  it('renders value propositions', () => {
    render(<Home {...mockProps} />);

    expect(screen.getByText('Performance Focused')).toBeInTheDocument();
    expect(screen.getByText('Expert Team')).toBeInTheDocument();
    expect(screen.getByText('Advanced Tracking')).toBeInTheDocument();
  });

  it('sets correct page title', () => {
    render(<Home {...mockProps} />);

    expect(document.title).toBe('ConvertOKit - Digital Marketing Agency');
  });
});
