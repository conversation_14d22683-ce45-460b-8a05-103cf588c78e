import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type User, type Consultation, type Payment } from '@/types';
import { Head, usePage } from '@inertiajs/react';
import { AdminDashboard } from '@/components/dashboard/admin-dashboard';
import { ClientDashboard } from '@/components/dashboard/client-dashboard';
import { TeamMemberDashboard } from '@/components/dashboard/team-member-dashboard';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

interface DashboardProps {
    user: User;
    role: 'admin' | 'client' | 'team_member';
    stats: Record<string, number>;
    recent_consultations?: Consultation[];
    recent_payments?: Payment[];
    upcoming_consultations?: Consultation[];
    assigned_consultations?: Consultation[];
}

export default function Dashboard() {
    const { props } = usePage<DashboardProps>();
    const { user, role, stats, recent_consultations, recent_payments, upcoming_consultations, assigned_consultations } = props;

    const renderDashboard = () => {
        switch (role) {
            case 'admin':
                return (
                    <AdminDashboard
                        stats={stats}
                        recentConsultations={recent_consultations || []}
                        recentPayments={recent_payments || []}
                    />
                );
            case 'team_member':
                return (
                    <TeamMemberDashboard
                        user={user}
                        stats={stats}
                        assignedConsultations={assigned_consultations || []}
                    />
                );
            case 'client':
            default:
                return (
                    <ClientDashboard
                        user={user}
                        stats={stats}
                        upcomingConsultations={upcoming_consultations || []}
                        recentConsultations={recent_consultations || []}
                        recentPayments={recent_payments || []}
                    />
                );
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6 overflow-x-auto">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">
                            Welcome back, {user.name}!
                        </h1>
                        <p className="text-muted-foreground">
                            Here's what's happening with your {role === 'admin' ? 'business' : role === 'team_member' ? 'assignments' : 'projects'} today.
                        </p>
                    </div>
                </div>

                {renderDashboard()}
            </div>
        </AppLayout>
    );
}
