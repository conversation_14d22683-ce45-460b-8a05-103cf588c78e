import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Mail, Phone, Calendar, User, Shield, Edit } from 'lucide-react';
import { type User, type Consultation, type Payment } from '@/types';

interface UserShowProps {
    user: User & {
        consultations?: Consultation[];
        payments?: Payment[];
        team_member?: {
            id: number;
            position: string;
            expertise: string[];
            is_active: boolean;
        };
    };
    stats: {
        total_consultations: number;
        total_payments: number;
        total_spent: number;
        last_login?: string;
    };
}

export default function UserShow({ user, stats }: UserShowProps) {
    const getRoleBadgeVariant = (role: string) => {
        switch (role) {
            case 'admin':
                return 'destructive';
            case 'team_member':
                return 'secondary';
            case 'client':
                return 'default';
            default:
                return 'outline';
        }
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    return (
        <AppLayout>
            <Head title={`User: ${user.name}`} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Users
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">{user.name}</h1>
                            <p className="text-muted-foreground">User Details</p>
                        </div>
                    </div>
                    <Button>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit User
                    </Button>
                </div>

                <div className="grid gap-6 md:grid-cols-3">
                    {/* User Profile */}
                    <div className="md:col-span-1">
                        <Card>
                            <CardHeader>
                                <CardTitle>Profile Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="flex flex-col items-center space-y-4">
                                    <Avatar className="h-20 w-20">
                                        <AvatarFallback className="text-lg">
                                            {getInitials(user.name)}
                                        </AvatarFallback>
                                    </Avatar>
                                    <div className="text-center">
                                        <h3 className="text-lg font-semibold">{user.name}</h3>
                                        <Badge variant={getRoleBadgeVariant(user.role)}>
                                            {user.role.replace('_', ' ')}
                                        </Badge>
                                    </div>
                                </div>

                                <Separator />

                                <div className="space-y-4">
                                    <div className="flex items-center space-x-3">
                                        <Mail className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Email</p>
                                            <p className="text-sm text-muted-foreground">{user.email}</p>
                                        </div>
                                    </div>

                                    {user.phone && (
                                        <div className="flex items-center space-x-3">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <div>
                                                <p className="text-sm font-medium">Phone</p>
                                                <p className="text-sm text-muted-foreground">{user.phone}</p>
                                            </div>
                                        </div>
                                    )}

                                    <div className="flex items-center space-x-3">
                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Joined</p>
                                            <p className="text-sm text-muted-foreground">
                                                {formatDate(user.created_at)}
                                            </p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-3">
                                        <Shield className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium">Email Verified</p>
                                            <p className="text-sm text-muted-foreground">
                                                {user.email_verified_at ? 'Yes' : 'No'}
                                            </p>
                                        </div>
                                    </div>

                                    {stats.last_login && (
                                        <div className="flex items-center space-x-3">
                                            <User className="h-4 w-4 text-muted-foreground" />
                                            <div>
                                                <p className="text-sm font-medium">Last Login</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {formatDate(stats.last_login)}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {user.team_member && (
                                    <>
                                        <Separator />
                                        <div>
                                            <h4 className="text-sm font-medium mb-2">Team Member Info</h4>
                                            <div className="space-y-2">
                                                <p className="text-sm">
                                                    <span className="font-medium">Position:</span> {user.team_member.position}
                                                </p>
                                                <p className="text-sm">
                                                    <span className="font-medium">Status:</span>{' '}
                                                    <Badge variant={user.team_member.is_active ? 'default' : 'secondary'}>
                                                        {user.team_member.is_active ? 'Active' : 'Inactive'}
                                                    </Badge>
                                                </p>
                                                {user.team_member.expertise && user.team_member.expertise.length > 0 && (
                                                    <div>
                                                        <p className="text-sm font-medium mb-1">Expertise:</p>
                                                        <div className="flex flex-wrap gap-1">
                                                            {user.team_member.expertise.map((skill, index) => (
                                                                <Badge key={index} variant="outline" className="text-xs">
                                                                    {skill}
                                                                </Badge>
                                                            ))}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Stats and Activity */}
                    <div className="md:col-span-2 space-y-6">
                        {/* Stats Cards */}
                        <div className="grid gap-4 md:grid-cols-3">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Total Consultations</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats.total_consultations}</div>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Total Payments</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{stats.total_payments}</div>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{formatCurrency(stats.total_spent)}</div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Recent Activity */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Recent Activity</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {user.consultations && user.consultations.length > 0 ? (
                                        user.consultations.slice(0, 5).map((consultation) => (
                                            <div key={consultation.id} className="flex items-center justify-between">
                                                <div>
                                                    <p className="text-sm font-medium">
                                                        Consultation #{consultation.id}
                                                    </p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {formatDate(consultation.scheduled_at)}
                                                    </p>
                                                </div>
                                                <Badge variant="outline">
                                                    {consultation.status}
                                                </Badge>
                                            </div>
                                        ))
                                    ) : (
                                        <p className="text-sm text-muted-foreground">No recent activity</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
