import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Mail, Send, Users, Eye, Edit, Trash2, Plus } from 'lucide-react';

interface NewsletterCampaign {
    id: number;
    subject: string;
    content: string;
    status: 'draft' | 'scheduled' | 'sent';
    sent_at?: string;
    scheduled_at?: string;
    recipients_count: number;
    open_rate?: number;
    click_rate?: number;
    created_at: string;
}

interface NewsletterSubscriber {
    id: number;
    email: string;
    status: 'active' | 'inactive';
    subscribed_at: string;
}

interface NewsletterProps {
    campaigns: {
        data: NewsletterCampaign[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    subscribers: {
        data: NewsletterSubscriber[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    stats: {
        total_campaigns: number;
        sent_campaigns: number;
        total_subscribers: number;
        active_subscribers: number;
        avg_open_rate: number;
        avg_click_rate: number;
    };
}

export default function Newsletter({ campaigns, subscribers, stats }: NewsletterProps) {
    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'sent':
                return 'default';
            case 'scheduled':
                return 'secondary';
            case 'draft':
                return 'outline';
            case 'active':
                return 'default';
            case 'inactive':
                return 'secondary';
            default:
                return 'secondary';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const formatPercentage = (value: number) => {
        return `${(value * 100).toFixed(1)}%`;
    };

    return (
        <AppLayout>
            <Head title="Newsletter Management" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Newsletter</h1>
                        <p className="text-muted-foreground">
                            Manage newsletter campaigns and subscribers
                        </p>
                    </div>
                    <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        Create Campaign
                    </Button>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
                            <Mail className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_campaigns}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Sent</CardTitle>
                            <Send className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.sent_campaigns}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Subscribers</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_subscribers}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.active_subscribers}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Avg Open Rate</CardTitle>
                            <Eye className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatPercentage(stats.avg_open_rate)}</div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Avg Click Rate</CardTitle>
                            <Eye className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatPercentage(stats.avg_click_rate)}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Campaigns */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Campaigns</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {campaigns.data.map((campaign) => (
                                <div
                                    key={campaign.id}
                                    className="flex items-center justify-between rounded-lg border p-4"
                                >
                                    <div className="flex items-center space-x-4">
                                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                                            <Mail className="h-5 w-5" />
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium">{campaign.subject}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {campaign.recipients_count} recipients
                                            </p>
                                            {campaign.open_rate && (
                                                <p className="text-xs text-muted-foreground">
                                                    Open: {formatPercentage(campaign.open_rate)} • 
                                                    Click: {formatPercentage(campaign.click_rate || 0)}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-4">
                                        <div className="text-right">
                                            <p className="text-sm text-muted-foreground">
                                                {campaign.status === 'sent' && campaign.sent_at
                                                    ? `Sent ${formatDate(campaign.sent_at)}`
                                                    : campaign.status === 'scheduled' && campaign.scheduled_at
                                                    ? `Scheduled ${formatDate(campaign.scheduled_at)}`
                                                    : `Created ${formatDate(campaign.created_at)}`}
                                            </p>
                                        </div>
                                        <Badge variant={getStatusBadgeVariant(campaign.status)}>
                                            {campaign.status}
                                        </Badge>
                                        <div className="flex space-x-2">
                                            <Button variant="outline" size="sm">
                                                <Eye className="h-4 w-4" />
                                            </Button>
                                            {campaign.status === 'draft' && (
                                                <Button variant="outline" size="sm">
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                            )}
                                            <Button variant="outline" size="sm">
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {campaigns.data.length === 0 && (
                            <div className="text-center py-8">
                                <p className="text-muted-foreground">No campaigns found</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Quick Actions */}
                <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Create New Campaign</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium">Subject</label>
                                <Input placeholder="Enter campaign subject..." />
                            </div>
                            <div>
                                <label className="text-sm font-medium">Content</label>
                                <Textarea 
                                    placeholder="Enter campaign content..." 
                                    rows={4}
                                />
                            </div>
                            <div className="flex space-x-2">
                                <Button variant="outline" className="flex-1">
                                    Save Draft
                                </Button>
                                <Button className="flex-1">
                                    <Send className="mr-2 h-4 w-4" />
                                    Send Now
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Subscribers</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {subscribers.data.slice(0, 5).map((subscriber) => (
                                    <div
                                        key={subscriber.id}
                                        className="flex items-center justify-between"
                                    >
                                        <div>
                                            <p className="text-sm font-medium">{subscriber.email}</p>
                                            <p className="text-xs text-muted-foreground">
                                                {formatDate(subscriber.subscribed_at)}
                                            </p>
                                        </div>
                                        <Badge variant={getStatusBadgeVariant(subscriber.status)}>
                                            {subscriber.status}
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                            
                            {subscribers.data.length === 0 && (
                                <div className="text-center py-4">
                                    <p className="text-muted-foreground text-sm">No subscribers found</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
