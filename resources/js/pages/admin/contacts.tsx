import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Mail, Search, Filter, Eye, Trash2, MessageSquare, Users } from 'lucide-react';

interface ContactSubmission {
    id: number;
    name: string;
    email: string;
    subject: string;
    message: string;
    status: 'new' | 'read' | 'replied';
    created_at: string;
}

interface NewsletterSubscription {
    id: number;
    email: string;
    status: 'active' | 'inactive';
    subscribed_at: string;
}

interface ContactsProps {
    contactSubmissions: {
        data: ContactSubmission[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    newsletterSubscriptions: {
        data: NewsletterSubscription[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    filters: {
        search?: string;
        status?: string;
    };
    stats: {
        total_contacts: number;
        new_contacts: number;
        total_subscribers: number;
        active_subscribers: number;
    };
}

export default function Contacts({ contactSubmissions, newsletterSubscriptions, filters, stats }: ContactsProps) {
    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'new':
                return 'default';
            case 'read':
                return 'secondary';
            case 'replied':
                return 'outline';
            case 'active':
                return 'default';
            case 'inactive':
                return 'secondary';
            default:
                return 'secondary';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <AppLayout>
            <Head title="Contacts & Newsletter" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Contacts & Newsletter</h1>
                        <p className="text-muted-foreground">
                            Manage contact submissions and newsletter subscriptions
                        </p>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Contacts</CardTitle>
                            <MessageSquare className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_contacts}</div>
                            <p className="text-xs text-muted-foreground">
                                All contact submissions
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">New Messages</CardTitle>
                            <Mail className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.new_contacts}</div>
                            <p className="text-xs text-muted-foreground">
                                Unread messages
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Newsletter Subscribers</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_subscribers}</div>
                            <p className="text-xs text-muted-foreground">
                                Total subscriptions
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Subscribers</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.active_subscribers}</div>
                            <p className="text-xs text-muted-foreground">
                                Currently subscribed
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Tabs */}
                <Tabs defaultValue="contacts" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="contacts">Contact Submissions</TabsTrigger>
                        <TabsTrigger value="newsletter">Newsletter Subscriptions</TabsTrigger>
                    </TabsList>

                    <TabsContent value="contacts" className="space-y-4">
                        {/* Filters */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Filter className="h-4 w-4" />
                                    Filters
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-col gap-4 md:flex-row">
                                    <div className="flex-1">
                                        <div className="relative">
                                            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                placeholder="Search contacts..."
                                                className="pl-8"
                                                defaultValue={filters.search}
                                            />
                                        </div>
                                    </div>
                                    <Select defaultValue={filters.status}>
                                        <SelectTrigger className="w-[180px]">
                                            <SelectValue placeholder="Status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Status</SelectItem>
                                            <SelectItem value="new">New</SelectItem>
                                            <SelectItem value="read">Read</SelectItem>
                                            <SelectItem value="replied">Replied</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Button variant="outline">Apply Filters</Button>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Contact Submissions */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Contact Submissions</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {contactSubmissions.data.map((contact) => (
                                        <div
                                            key={contact.id}
                                            className="flex items-center justify-between rounded-lg border p-4"
                                        >
                                            <div className="flex items-center space-x-4">
                                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                                                    <MessageSquare className="h-5 w-5" />
                                                </div>
                                                <div>
                                                    <p className="text-sm font-medium">{contact.name}</p>
                                                    <p className="text-sm text-muted-foreground">{contact.email}</p>
                                                    <p className="text-xs text-muted-foreground">{contact.subject}</p>
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-4">
                                                <div className="text-right">
                                                    <p className="text-sm text-muted-foreground">
                                                        {formatDate(contact.created_at)}
                                                    </p>
                                                </div>
                                                <Badge variant={getStatusBadgeVariant(contact.status)}>
                                                    {contact.status}
                                                </Badge>
                                                <div className="flex space-x-2">
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="outline" size="sm">
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {contactSubmissions.data.length === 0 && (
                                    <div className="text-center py-8">
                                        <p className="text-muted-foreground">No contact submissions found</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="newsletter" className="space-y-4">
                        {/* Newsletter Subscriptions */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Newsletter Subscriptions</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {newsletterSubscriptions.data.map((subscription) => (
                                        <div
                                            key={subscription.id}
                                            className="flex items-center justify-between rounded-lg border p-4"
                                        >
                                            <div className="flex items-center space-x-4">
                                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                                                    <Mail className="h-5 w-5" />
                                                </div>
                                                <div>
                                                    <p className="text-sm font-medium">{subscription.email}</p>
                                                    <p className="text-xs text-muted-foreground">
                                                        Subscribed {formatDate(subscription.subscribed_at)}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-4">
                                                <Badge variant={getStatusBadgeVariant(subscription.status)}>
                                                    {subscription.status}
                                                </Badge>
                                                <Button variant="outline" size="sm">
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                {newsletterSubscriptions.data.length === 0 && (
                                    <div className="text-center py-8">
                                        <p className="text-muted-foreground">No newsletter subscriptions found</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
