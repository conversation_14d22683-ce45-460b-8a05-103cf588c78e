import AdminLayout from '@/layouts/admin-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type BreadcrumbItem, type BlogPost } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { 
    Plus, 
    Eye,
    Edit,
    BookOpen,
    FileText,
    Users,
    BarChart3
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Blog',
        href: '/admin/blog',
    },
];

interface BlogIndexProps {
    stats: {
        total_posts: number;
        published_posts: number;
        draft_posts: number;
        total_categories: number;
    };
    recentPosts: BlogPost[];
}

export default function BlogIndex() {
    const { props } = usePage<BlogIndexProps>();
    const { stats, recentPosts } = props;

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
        });
    };

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            published: 'default',
            draft: 'secondary',
        };
        return <Badge variant={variants[status] || 'outline'}>{status}</Badge>;
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Blog Management" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6 overflow-x-auto">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Blog Management</h1>
                        <p className="text-muted-foreground">
                            Manage your blog posts, categories, and content.
                        </p>
                    </div>
                    
                    <div className="flex items-center gap-2">
                        <Button variant="outline" asChild>
                            <Link href="/admin/blog/categories">
                                Manage Categories
                            </Link>
                        </Button>
                        <Button asChild>
                            <Link href="/admin/blog/posts/create">
                                <Plus className="mr-2 h-4 w-4" />
                                New Post
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Stats Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
                            <BookOpen className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_posts}</div>
                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                <Link href="/admin/blog/posts">
                                    View All Posts
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Published</CardTitle>
                            <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.published_posts}</div>
                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                <Link href="/admin/blog/posts?status=published">
                                    View Published
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
                            <Edit className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.draft_posts}</div>
                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                <Link href="/admin/blog/posts?status=draft">
                                    View Drafts
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Categories</CardTitle>
                            <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_categories}</div>
                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                <Link href="/admin/blog/categories">
                                    Manage Categories
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>Common blog management tasks</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <Button variant="outline" asChild>
                                <Link href="/admin/blog/posts/create">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Create New Post
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/admin/blog/posts">
                                    <BookOpen className="mr-2 h-4 w-4" />
                                    All Posts
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/admin/blog/categories">
                                    <BarChart3 className="mr-2 h-4 w-4" />
                                    Categories
                                </Link>
                            </Button>
                            <Button variant="outline" asChild>
                                <Link href="/admin/blog/comments">
                                    <Users className="mr-2 h-4 w-4" />
                                    Comments
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Recent Posts */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Posts</CardTitle>
                        <CardDescription>Your latest blog posts</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {recentPosts.length > 0 ? (
                                recentPosts.map((post) => (
                                    <div key={post.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center gap-4">
                                            {post.featured_image && (
                                                <div className="w-16 h-16 rounded-lg overflow-hidden">
                                                    <img 
                                                        src={post.featured_image} 
                                                        alt={post.title}
                                                        className="w-full h-full object-cover"
                                                    />
                                                </div>
                                            )}
                                            <div className="space-y-1 flex-1">
                                                <div className="flex items-center gap-2">
                                                    <p className="text-sm font-medium">{post.title}</p>
                                                    {post.category && (
                                                        <Badge variant="outline" style={{ backgroundColor: post.category.color + '20', color: post.category.color }}>
                                                            {post.category.name}
                                                        </Badge>
                                                    )}
                                                </div>
                                                <p className="text-xs text-muted-foreground line-clamp-2">
                                                    {post.excerpt}
                                                </p>
                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    <span>By {post.author?.name}</span>
                                                    <span>{formatDate(post.created_at)}</span>
                                                    <span>{post.views_count} views</span>
                                                    {post.reading_time && <span>{post.reading_time} min read</span>}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div className="flex items-center gap-4">
                                            {getStatusBadge(post.status)}
                                            
                                            <div className="flex items-center gap-2">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/admin/blog/posts/${post.id}`}>
                                                        <Eye className="h-4 w-4" />
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/admin/blog/posts/${post.id}/edit`}>
                                                        <Edit className="h-4 w-4" />
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="text-center py-8">
                                    <BookOpen className="mx-auto h-12 w-12 text-muted-foreground" />
                                    <h3 className="mt-2 text-sm font-semibold">No blog posts yet</h3>
                                    <p className="mt-1 text-sm text-muted-foreground">
                                        Get started by creating your first blog post.
                                    </p>
                                    <div className="mt-6">
                                        <Button asChild>
                                            <Link href="/admin/blog/posts/create">
                                                <Plus className="mr-2 h-4 w-4" />
                                                Create Post
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </div>

                        {recentPosts.length > 0 && (
                            <div className="mt-4">
                                <Button variant="outline" size="sm" asChild>
                                    <Link href="/admin/blog/posts">View All Posts</Link>
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
