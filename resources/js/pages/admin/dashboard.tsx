import AdminLayout from '@/layouts/admin-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type BreadcrumbItem, type User, type Consultation, type Payment, type ContactSubmission } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { 
    Users, 
    Calendar, 
    DollarSign, 
    TrendingUp, 
    Building,
    BookOpen,
    Mail,
    UserCheck,
    ArrowUpRight
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin Dashboard',
        href: '/admin/dashboard',
    },
];

interface AdminDashboardProps {
    stats: {
        total_users: number;
        total_clients: number;
        total_team_members: number;
        total_services: number;
        total_blog_posts: number;
        total_consultations: number;
        pending_consultations: number;
        completed_consultations: number;
        total_revenue: number;
        monthly_revenue: number;
        pending_contacts: number;
        newsletter_subscribers: number;
    };
    recentActivity: {
        recent_users: User[];
        recent_consultations: Consultation[];
        recent_payments: Payment[];
        recent_contacts: ContactSubmission[];
    };
}

export default function AdminDashboard() {
    const { props } = usePage<AdminDashboardProps>();
    const { stats, recentActivity } = props;

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
        });
    };

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            pending: 'outline',
            confirmed: 'default',
            completed: 'secondary',
            cancelled: 'destructive',
            new: 'outline',
            read: 'secondary',
        };
        return <Badge variant={variants[status] || 'outline'}>{status}</Badge>;
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Admin Dashboard" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6 overflow-x-auto">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
                        <p className="text-muted-foreground">
                            Overview of your business performance and recent activity.
                        </p>
                    </div>
                </div>

                {/* Stats Grid */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_users}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.total_clients} clients, {stats.total_team_members} team
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.monthly_revenue)}</div>
                            <p className="text-xs text-muted-foreground">
                                Total: {formatCurrency(stats.total_revenue)}
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Consultations</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_consultations}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats.pending_consultations} pending
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Growth</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">+12.5%</div>
                            <p className="text-xs text-muted-foreground">
                                From last month
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Stats */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Services</CardTitle>
                            <Building className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_services}</div>
                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                <Link href="/admin/services">
                                    Manage <ArrowUpRight className="ml-1 h-3 w-3" />
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Blog Posts</CardTitle>
                            <BookOpen className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_blog_posts}</div>
                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                <Link href="/admin/blog">
                                    Manage <ArrowUpRight className="ml-1 h-3 w-3" />
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pending Contacts</CardTitle>
                            <Mail className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.pending_contacts}</div>
                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                <Link href="/admin/contacts">
                                    Review <ArrowUpRight className="ml-1 h-3 w-3" />
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Newsletter</CardTitle>
                            <UserCheck className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.newsletter_subscribers}</div>
                            <Button variant="link" size="sm" className="p-0 h-auto" asChild>
                                <Link href="/admin/newsletter">
                                    Manage <ArrowUpRight className="ml-1 h-3 w-3" />
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Activity */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Users</CardTitle>
                            <CardDescription>Latest user registrations</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentActivity.recent_users.length > 0 ? (
                                    recentActivity.recent_users.map((user) => (
                                        <div key={user.id} className="flex items-center justify-between">
                                            <div className="space-y-1">
                                                <p className="text-sm font-medium">{user.name}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {user.email} • {formatDate(user.created_at)}
                                                </p>
                                            </div>
                                            <Badge variant="outline">{user.role}</Badge>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-sm text-muted-foreground">No recent users</p>
                                )}
                            </div>
                            <div className="mt-4">
                                <Button variant="outline" size="sm" asChild>
                                    <Link href="/admin/users">View All Users</Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Consultations</CardTitle>
                            <CardDescription>Latest consultation bookings</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentActivity.recent_consultations.length > 0 ? (
                                    recentActivity.recent_consultations.map((consultation) => (
                                        <div key={consultation.id} className="flex items-center justify-between">
                                            <div className="space-y-1">
                                                <p className="text-sm font-medium">
                                                    {consultation.user?.name || 'Unknown User'}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    {consultation.service?.title} • {formatDate(consultation.consultation_date)}
                                                </p>
                                            </div>
                                            {getStatusBadge(consultation.status)}
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-sm text-muted-foreground">No recent consultations</p>
                                )}
                            </div>
                            <div className="mt-4">
                                <Button variant="outline" size="sm" asChild>
                                    <Link href="/admin/consultations">View All Consultations</Link>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AdminLayout>
    );
}
