import AdminLayout from '@/layouts/admin-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { type BreadcrumbItem, type Service } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { 
    Search, 
    Plus, 
    Eye,
    Edit,
    Trash2,
    Filter,
    Building
} from 'lucide-react';
import { useState } from 'react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Services',
        href: '/admin/services',
    },
];

interface ServicesIndexProps {
    services: {
        data: Service[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        category?: string;
        search?: string;
    };
}

export default function ServicesIndex() {
    const { props } = usePage<ServicesIndexProps>();
    const { services, filters } = props;
    
    const [search, setSearch] = useState(filters.search || '');
    const [category, setCategory] = useState(filters.category || '');

    const formatCurrency = (priceRange: string | undefined) => {
        return priceRange || 'Contact for pricing';
    };

    const getStatusBadge = (isActive: boolean) => {
        return (
            <Badge variant={isActive ? 'default' : 'secondary'}>
                {isActive ? 'Active' : 'Inactive'}
            </Badge>
        );
    };

    const handleSearch = () => {
        router.get('/admin/services', {
            search: search || undefined,
            category: category || undefined,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleClearFilters = () => {
        setSearch('');
        setCategory('');
        router.get('/admin/services', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Service Management" />
            
            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6 overflow-x-auto">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Service Management</h1>
                        <p className="text-muted-foreground">
                            Manage your service offerings and pricing.
                        </p>
                    </div>
                    
                    <Button asChild>
                        <Link href="/admin/services/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Service
                        </Link>
                    </Button>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Filters</CardTitle>
                        <CardDescription>Search and filter services</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center gap-4">
                            <div className="flex-1">
                                <Input
                                    placeholder="Search by title or description..."
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                />
                            </div>
                            <Select value={category} onValueChange={setCategory}>
                                <SelectTrigger className="w-[200px]">
                                    <SelectValue placeholder="Filter by category" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All Categories</SelectItem>
                                    <SelectItem value="Meta Ads">Meta Ads</SelectItem>
                                    <SelectItem value="Analytics">Analytics</SelectItem>
                                    <SelectItem value="Tracking">Tracking</SelectItem>
                                    <SelectItem value="Consulting">Consulting</SelectItem>
                                </SelectContent>
                            </Select>
                            <Button onClick={handleSearch}>
                                <Search className="mr-2 h-4 w-4" />
                                Search
                            </Button>
                            <Button variant="outline" onClick={handleClearFilters}>
                                <Filter className="mr-2 h-4 w-4" />
                                Clear
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Services List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Services ({services.total})</CardTitle>
                        <CardDescription>All available services</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {services.data.length > 0 ? (
                                services.data.map((service) => (
                                    <div key={service.id} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center gap-4">
                                            <div className="w-12 h-12 rounded-lg bg-muted flex items-center justify-center">
                                                <Building className="h-6 w-6 text-muted-foreground" />
                                            </div>
                                            <div className="space-y-1 flex-1">
                                                <div className="flex items-center gap-2">
                                                    <p className="text-sm font-medium">{service.title}</p>
                                                    <Badge variant="outline">{service.category}</Badge>
                                                </div>
                                                <p className="text-xs text-muted-foreground line-clamp-2">
                                                    {service.description}
                                                </p>
                                                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                                    <span>Price: {formatCurrency(service.price_range)}</span>
                                                    <span>Sort: {service.sort_order}</span>
                                                    {service.features && service.features.length > 0 && (
                                                        <span>{service.features.length} features</span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div className="flex items-center gap-4">
                                            {getStatusBadge(service.is_active)}
                                            
                                            <div className="flex items-center gap-2">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/admin/services/${service.id}`}>
                                                        <Eye className="h-4 w-4" />
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/admin/services/${service.id}/edit`}>
                                                        <Edit className="h-4 w-4" />
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm">
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))
                            ) : (
                                <div className="text-center py-8">
                                    <Building className="mx-auto h-12 w-12 text-muted-foreground" />
                                    <h3 className="mt-2 text-sm font-semibold">No services found</h3>
                                    <p className="mt-1 text-sm text-muted-foreground">
                                        Get started by creating a new service.
                                    </p>
                                    <div className="mt-6">
                                        <Button asChild>
                                            <Link href="/admin/services/create">
                                                <Plus className="mr-2 h-4 w-4" />
                                                Add Service
                                            </Link>
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Pagination */}
                        {services.last_page > 1 && (
                            <div className="flex items-center justify-between mt-6">
                                <p className="text-sm text-muted-foreground">
                                    Showing {((services.current_page - 1) * services.per_page) + 1} to {Math.min(services.current_page * services.per_page, services.total)} of {services.total} services
                                </p>
                                
                                <div className="flex items-center gap-2">
                                    {services.links.map((link, index) => (
                                        <Button
                                            key={index}
                                            variant={link.active ? "default" : "outline"}
                                            size="sm"
                                            disabled={!link.url}
                                            onClick={() => link.url && router.get(link.url)}
                                            dangerouslySetInnerHTML={{ __html: link.label }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
