import { render, screen } from '@testing-library/react';
import { ProtectedRoute, <PERSON>minOnly, <PERSON>lientOnly, TeamMemberOnly, usePermissions } from '@/components/protected-route';
import { type User } from '@/types';
import { vi } from 'vitest';

// Mock Inertia usePage hook
const mockUsePage = vi.fn();
vi.mock('@inertiajs/react', () => ({
    usePage: () => mockUsePage(),
    Link: ({ children, href, ...props }: Record<string, unknown>) => (
        <a href={href as string} {...props}>
            {children}
        </a>
    ),
}));

const mockAdminUser: User = {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
};

const mockClientUser: User = {
    id: '2',
    name: 'Client User',
    email: '<EMAIL>',
    role: 'client',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
};

const mockTeamMemberUser: User = {
    id: '3',
    name: 'Team Member',
    email: '<EMAIL>',
    role: 'team_member',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
};

describe('ProtectedRoute', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders children when user has required role', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockAdminUser }
            }
        });

        render(
            <ProtectedRoute allowedRoles={['admin']}>
                <div>Protected Content</div>
            </ProtectedRoute>
        );

        expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('shows access denied when user lacks required role', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(
            <ProtectedRoute allowedRoles={['admin']}>
                <div>Protected Content</div>
            </ProtectedRoute>
        );

        expect(screen.getByText('Access Denied')).toBeInTheDocument();
        expect(screen.getByText(/You don't have permission/)).toBeInTheDocument();
        expect(screen.getByText(/Required role: admin/)).toBeInTheDocument();
        expect(screen.getByText(/Your role: client/)).toBeInTheDocument();
        expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    it('shows authentication required when user is not authenticated', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: null }
            }
        });

        render(
            <ProtectedRoute requireAuth={true}>
                <div>Protected Content</div>
            </ProtectedRoute>
        );

        expect(screen.getByText('Authentication Required')).toBeInTheDocument();
        expect(screen.getByText(/You need to be logged in/)).toBeInTheDocument();
        expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    it('allows access when multiple roles are specified and user has one', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockTeamMemberUser }
            }
        });

        render(
            <ProtectedRoute allowedRoles={['admin', 'team_member']}>
                <div>Protected Content</div>
            </ProtectedRoute>
        );

        expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('renders children when no role restrictions are set', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(
            <ProtectedRoute>
                <div>Public Content</div>
            </ProtectedRoute>
        );

        expect(screen.getByText('Public Content')).toBeInTheDocument();
    });

    it('renders custom fallback when provided', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        const customFallback = <div>Custom Access Denied</div>;

        render(
            <ProtectedRoute allowedRoles={['admin']} fallback={customFallback}>
                <div>Protected Content</div>
            </ProtectedRoute>
        );

        expect(screen.getByText('Custom Access Denied')).toBeInTheDocument();
        expect(screen.queryByText('Access Denied')).not.toBeInTheDocument();
    });
});

describe('AdminOnly', () => {
    it('allows admin users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockAdminUser }
            }
        });

        render(
            <AdminOnly>
                <div>Admin Content</div>
            </AdminOnly>
        );

        expect(screen.getByText('Admin Content')).toBeInTheDocument();
    });

    it('blocks non-admin users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(
            <AdminOnly>
                <div>Admin Content</div>
            </AdminOnly>
        );

        expect(screen.getByText('Access Denied')).toBeInTheDocument();
        expect(screen.queryByText('Admin Content')).not.toBeInTheDocument();
    });
});

describe('ClientOnly', () => {
    it('allows client users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(
            <ClientOnly>
                <div>Client Content</div>
            </ClientOnly>
        );

        expect(screen.getByText('Client Content')).toBeInTheDocument();
    });

    it('blocks non-client users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockAdminUser }
            }
        });

        render(
            <ClientOnly>
                <div>Client Content</div>
            </ClientOnly>
        );

        expect(screen.getByText('Access Denied')).toBeInTheDocument();
        expect(screen.queryByText('Client Content')).not.toBeInTheDocument();
    });
});

describe('TeamMemberOnly', () => {
    it('allows team member users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockTeamMemberUser }
            }
        });

        render(
            <TeamMemberOnly>
                <div>Team Content</div>
            </TeamMemberOnly>
        );

        expect(screen.getByText('Team Content')).toBeInTheDocument();
    });

    it('blocks non-team member users', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(
            <TeamMemberOnly>
                <div>Team Content</div>
            </TeamMemberOnly>
        );

        expect(screen.getByText('Access Denied')).toBeInTheDocument();
        expect(screen.queryByText('Team Content')).not.toBeInTheDocument();
    });
});

describe('usePermissions hook', () => {
    const TestComponent = () => {
        const permissions = usePermissions();
        
        return (
            <div>
                <div data-testid="is-admin">{permissions.isAdmin.toString()}</div>
                <div data-testid="is-client">{permissions.isClient.toString()}</div>
                <div data-testid="is-team-member">{permissions.isTeamMember.toString()}</div>
                <div data-testid="has-admin-role">{permissions.hasRole('admin').toString()}</div>
                <div data-testid="can-access-admin">{permissions.canAccess(['admin']).toString()}</div>
                <div data-testid="has-any-role">{permissions.hasAnyRole(['admin', 'client']).toString()}</div>
            </div>
        );
    };

    it('returns correct permissions for admin user', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockAdminUser }
            }
        });

        render(<TestComponent />);

        expect(screen.getByTestId('is-admin')).toHaveTextContent('true');
        expect(screen.getByTestId('is-client')).toHaveTextContent('false');
        expect(screen.getByTestId('is-team-member')).toHaveTextContent('false');
        expect(screen.getByTestId('has-admin-role')).toHaveTextContent('true');
        expect(screen.getByTestId('can-access-admin')).toHaveTextContent('true');
        expect(screen.getByTestId('has-any-role')).toHaveTextContent('true');
    });

    it('returns correct permissions for client user', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockClientUser }
            }
        });

        render(<TestComponent />);

        expect(screen.getByTestId('is-admin')).toHaveTextContent('false');
        expect(screen.getByTestId('is-client')).toHaveTextContent('true');
        expect(screen.getByTestId('is-team-member')).toHaveTextContent('false');
        expect(screen.getByTestId('has-admin-role')).toHaveTextContent('false');
        expect(screen.getByTestId('can-access-admin')).toHaveTextContent('false');
        expect(screen.getByTestId('has-any-role')).toHaveTextContent('true');
    });

    it('returns correct permissions for team member user', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: mockTeamMemberUser }
            }
        });

        render(<TestComponent />);

        expect(screen.getByTestId('is-admin')).toHaveTextContent('false');
        expect(screen.getByTestId('is-client')).toHaveTextContent('false');
        expect(screen.getByTestId('is-team-member')).toHaveTextContent('true');
        expect(screen.getByTestId('has-admin-role')).toHaveTextContent('false');
        expect(screen.getByTestId('can-access-admin')).toHaveTextContent('false');
        expect(screen.getByTestId('has-any-role')).toHaveTextContent('false');
    });

    it('handles null user correctly', () => {
        mockUsePage.mockReturnValue({
            props: {
                auth: { user: null }
            }
        });

        render(<TestComponent />);

        expect(screen.getByTestId('is-admin')).toHaveTextContent('false');
        expect(screen.getByTestId('is-client')).toHaveTextContent('false');
        expect(screen.getByTestId('is-team-member')).toHaveTextContent('false');
        expect(screen.getByTestId('has-admin-role')).toHaveTextContent('false');
        expect(screen.getByTestId('can-access-admin')).toHaveTextContent('false');
        expect(screen.getByTestId('has-any-role')).toHaveTextContent('false');
    });
});
