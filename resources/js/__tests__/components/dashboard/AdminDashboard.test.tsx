import { render, screen } from '@testing-library/react';
import { AdminDashboard } from '@/components/dashboard/admin-dashboard';
import { type Consultation, type Payment } from '@/types';
import { vi } from 'vitest';

// Mock Inertia Link component
vi.mock('@inertiajs/react', () => ({
    Link: ({ children, href, ...props }: Record<string, unknown>) => (
        <a href={href as string} {...props}>
            {children}
        </a>
    ),
}));

const mockStats = {
    total_users: 150,
    total_clients: 120,
    total_team_members: 25,
    total_consultations: 300,
    pending_consultations: 15,
    completed_consultations: 250,
    total_revenue: 50000,
    monthly_revenue: 8500,
};

const mockRecentConsultations: Consultation[] = [
    {
        id: '1',
        user: { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'client' },
        service: { id: '1', title: 'Meta Ads Consultation', slug: 'meta-ads' },
        status: 'pending',
        consultation_date: '2024-01-15T10:00:00Z',
        duration: 60,
        created_at: '2024-01-10T10:00:00Z',
        updated_at: '2024-01-10T10:00:00Z',
    },
    {
        id: '2',
        user: { id: '2', name: 'Jane <PERSON>', email: '<EMAIL>', role: 'client' },
        service: { id: '2', title: 'Analytics Setup', slug: 'analytics-setup' },
        status: 'completed',
        consultation_date: '2024-01-12T14:00:00Z',
        duration: 90,
        created_at: '2024-01-08T10:00:00Z',
        updated_at: '2024-01-12T15:30:00Z',
    },
];

const mockRecentPayments: Payment[] = [
    {
        id: '1',
        user: { id: '1', name: 'John Doe', email: '<EMAIL>', role: 'client' },
        consultation: {
            id: '1',
            service: { id: '1', title: 'Meta Ads Consultation', slug: 'meta-ads' },
        },
        amount: 150.00,
        status: 'completed',
        payment_method: 'stripe',
        created_at: '2024-01-10T10:00:00Z',
        updated_at: '2024-01-10T10:00:00Z',
    },
    {
        id: '2',
        user: { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'client' },
        consultation: {
            id: '2',
            service: { id: '2', title: 'Analytics Setup', slug: 'analytics-setup' },
        },
        amount: 200.00,
        status: 'pending',
        payment_method: 'stripe',
        created_at: '2024-01-12T10:00:00Z',
        updated_at: '2024-01-12T10:00:00Z',
    },
];

describe('AdminDashboard', () => {
    it('renders admin dashboard with stats', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        // Check if stats are displayed
        expect(screen.getByText('150')).toBeInTheDocument(); // total_users
        expect(screen.getByText('120 clients, 25 team members')).toBeInTheDocument();
        expect(screen.getByText('$8,500.00')).toBeInTheDocument(); // monthly_revenue
        expect(screen.getByText('Total: $50,000.00')).toBeInTheDocument();
        expect(screen.getByText('300')).toBeInTheDocument(); // total_consultations
        expect(screen.getByText('15 pending, 250 completed')).toBeInTheDocument();
    });

    it('displays recent consultations correctly', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getAllByText('John Doe')).toHaveLength(2);
        expect(screen.getAllByText('Jane Smith')).toHaveLength(2);
        expect(screen.getByText(/Meta Ads Consultation/)).toBeInTheDocument();
        expect(screen.getByText(/Analytics Setup/)).toBeInTheDocument();
    });

    it('displays recent payments correctly', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText(/\$150\.00/)).toBeInTheDocument();
        expect(screen.getByText(/\$200\.00/)).toBeInTheDocument();
    });

    it('shows correct status badges', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getAllByText('pending')).toHaveLength(2);
        expect(screen.getAllByText('completed')).toHaveLength(2);
    });

    it('renders quick action buttons', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText('Manage Users')).toBeInTheDocument();
        expect(screen.getByText('Manage Services')).toBeInTheDocument();
        expect(screen.getByText('Manage Blog')).toBeInTheDocument();
        expect(screen.getByText('View Analytics')).toBeInTheDocument();
    });

    it('has correct links for quick actions', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        const manageUsersLink = screen.getByText('Manage Users').closest('a');
        expect(manageUsersLink).toHaveAttribute('href', '/admin/users');

        const manageServicesLink = screen.getByText('Manage Services').closest('a');
        expect(manageServicesLink).toHaveAttribute('href', '/admin/services');
    });

    it('handles empty consultations gracefully', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={[]}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText('No recent consultations')).toBeInTheDocument();
    });

    it('handles empty payments gracefully', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={[]}
            />
        );

        expect(screen.getByText('No recent payments')).toBeInTheDocument();
    });

    it('formats currency correctly', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        // Check currency formatting
        expect(screen.getByText(/\$8,500\.00/)).toBeInTheDocument();
        expect(screen.getByText(/Total: \$50,000\.00/)).toBeInTheDocument();
        expect(screen.getByText(/\$150\.00/)).toBeInTheDocument();
        expect(screen.getByText(/\$200\.00/)).toBeInTheDocument();
    });

    it('formats dates correctly', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        // Check date formatting (should show formatted dates)
        expect(screen.getByText(/Jan 15, 2024/)).toBeInTheDocument();
        expect(screen.getAllByText(/Jan 12, 2024/)).toHaveLength(2);
    });

    it('displays correct icons for stats', () => {
        render(
            <AdminDashboard
                stats={mockStats}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        // Check if stat cards are present
        expect(screen.getByText('Total Users')).toBeInTheDocument();
        expect(screen.getByText('Consultations')).toBeInTheDocument();
        expect(screen.getByText('Monthly Revenue')).toBeInTheDocument();
        expect(screen.getByText('Growth')).toBeInTheDocument();
    });
});
