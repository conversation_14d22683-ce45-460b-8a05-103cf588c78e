import AppLayoutTemplate from '@/layouts/app/app-sidebar-layout';
import AdminLayout from '@/layouts/admin-layout';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { type ReactNode } from 'react';
import { usePage } from '@inertiajs/react';

interface AppLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
    forceRegularLayout?: boolean;
}

export default ({ children, breadcrumbs, forceRegularLayout = false, ...props }: AppLayoutProps) => {
    const { auth } = usePage<SharedData>().props;
    const user = auth?.user;

    // Use admin layout for admin users unless forced to use regular layout
    if (!forceRegularLayout && user?.role === 'admin' && window.location.pathname.startsWith('/admin')) {
        return (
            <AdminLayout breadcrumbs={breadcrumbs} {...props}>
                {children}
            </AdminLayout>
        );
    }

    return (
        <AppLayoutTemplate breadcrumbs={breadcrumbs} {...props}>
            {children}
        </AppLayoutTemplate>
    );
};
