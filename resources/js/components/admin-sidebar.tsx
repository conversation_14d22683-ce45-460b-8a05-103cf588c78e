import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type NavGroup } from '@/types';
import { Link } from '@inertiajs/react';
import { 
    LayoutGrid, 
    Users, 
    Building, 
    BookOpen, 
    UserCheck, 
    Calendar, 
    CreditCard, 
    BarChart3, 
    Mail, 
    Settings,
    Home
} from 'lucide-react';
import AppLogo from './app-logo';

const mainNavGroups: NavGroup[] = [
    {
        title: 'Overview',
        items: [
            {
                title: 'Dashboard',
                href: '/admin/dashboard',
                icon: LayoutGrid,
            },
            {
                title: 'Analytics',
                href: '/admin/analytics',
                icon: BarChart3,
            },
        ],
    },
    {
        title: 'Management',
        items: [
            {
                title: 'Users',
                href: '/admin/users',
                icon: Users,
            },
            {
                title: 'Services',
                href: '/admin/services',
                icon: Building,
            },
            {
                title: 'Team',
                href: '/admin/team',
                icon: UserCheck,
            },
            {
                title: 'Blog',
                href: '/admin/blog',
                icon: BookOpen,
            },
        ],
    },
    {
        title: 'Operations',
        items: [
            {
                title: 'Consultations',
                href: '/admin/consultations',
                icon: Calendar,
            },
            {
                title: 'Payments',
                href: '/admin/payments',
                icon: CreditCard,
            },
            {
                title: 'Contacts',
                href: '/admin/contacts',
                icon: Mail,
            },
            {
                title: 'Newsletter',
                href: '/admin/newsletter',
                icon: Mail,
            },
        ],
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Public Site',
        href: '/',
        icon: Home,
    },
    {
        title: 'Settings',
        href: '/admin/settings',
        icon: Settings,
    },
];

export function AdminSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/admin/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <div className="space-y-4">
                    {mainNavGroups.map((group) => (
                        <div key={group.title}>
                            <div className="px-3 py-2">
                                <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
                                    {group.title}
                                </h2>
                            </div>
                            <NavMain items={group.items} />
                        </div>
                    ))}
                </div>
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
