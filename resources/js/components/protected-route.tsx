import { type ReactNode } from 'react';
import { type User } from '@/types';
import { usePage } from '@inertiajs/react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { ShieldAlert, ArrowLeft } from 'lucide-react';

interface ProtectedRouteProps {
    children: ReactNode;
    allowedRoles?: Array<'admin' | 'client' | 'team_member'>;
    requireAuth?: boolean;
    fallback?: ReactNode;
}

export function ProtectedRoute({ 
    children, 
    allowedRoles = [], 
    requireAuth = true,
    fallback 
}: ProtectedRouteProps) {
    const { auth } = usePage<{ auth: { user: User } }>().props;
    const user = auth?.user;

    // Check if authentication is required and user is not authenticated
    if (requireAuth && !user) {
        return fallback || (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="max-w-md w-full space-y-8">
                    <Alert>
                        <ShieldAlert className="h-4 w-4" />
                        <AlertTitle>Authentication Required</AlertTitle>
                        <AlertDescription>
                            You need to be logged in to access this page.
                        </AlertDescription>
                    </Alert>
                    <div className="flex gap-4">
                        <Button asChild>
                            <Link href="/login">Sign In</Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Go Home
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        );
    }

    // Check if user has required role
    if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
        return fallback || (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="max-w-md w-full space-y-8">
                    <Alert variant="destructive">
                        <ShieldAlert className="h-4 w-4" />
                        <AlertTitle>Access Denied</AlertTitle>
                        <AlertDescription>
                            You don't have permission to access this page. 
                            Required role: {allowedRoles.join(' or ')}.
                            Your role: {user.role}.
                        </AlertDescription>
                    </Alert>
                    <div className="flex gap-4">
                        <Button variant="outline" asChild>
                            <Link href="/dashboard">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Go to Dashboard
                            </Link>
                        </Button>
                        <Button variant="outline" asChild>
                            <Link href="/">Go Home</Link>
                        </Button>
                    </div>
                </div>
            </div>
        );
    }

    return <>{children}</>;
}

// Higher-order component for role-based access
export function withRoleProtection<P extends object>(
    Component: React.ComponentType<P>,
    allowedRoles: Array<'admin' | 'client' | 'team_member'>
) {
    return function ProtectedComponent(props: P) {
        return (
            <ProtectedRoute allowedRoles={allowedRoles}>
                <Component {...props} />
            </ProtectedRoute>
        );
    };
}

// Specific role protection components
export function AdminOnly({ children }: { children: ReactNode }) {
    return (
        <ProtectedRoute allowedRoles={['admin']}>
            {children}
        </ProtectedRoute>
    );
}

export function TeamMemberOnly({ children }: { children: ReactNode }) {
    return (
        <ProtectedRoute allowedRoles={['team_member']}>
            {children}
        </ProtectedRoute>
    );
}

export function ClientOnly({ children }: { children: ReactNode }) {
    return (
        <ProtectedRoute allowedRoles={['client']}>
            {children}
        </ProtectedRoute>
    );
}

export function TeamOrAdmin({ children }: { children: ReactNode }) {
    return (
        <ProtectedRoute allowedRoles={['admin', 'team_member']}>
            {children}
        </ProtectedRoute>
    );
}

// Hook for checking user permissions
export function usePermissions() {
    const { auth } = usePage<{ auth: { user: User } }>().props;
    const user = auth?.user;

    return {
        user,
        isAdmin: user?.role === 'admin',
        isTeamMember: user?.role === 'team_member',
        isClient: user?.role === 'client',
        hasRole: (role: 'admin' | 'client' | 'team_member') => user?.role === role,
        hasAnyRole: (roles: Array<'admin' | 'client' | 'team_member'>) => 
            user ? roles.includes(user.role) : false,
        canAccess: (allowedRoles: Array<'admin' | 'client' | 'team_member'>) =>
            user ? allowedRoles.includes(user.role) : false,
    };
}
