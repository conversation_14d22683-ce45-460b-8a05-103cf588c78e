import React from 'react';
import { render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import SeoHead from '@/components/seo-head';

// Mock the Head component to render children directly in the container
vi.mock('@inertiajs/react', () => ({
  Head: ({ children, title }: { children?: React.ReactNode; title?: string }) => {
    // Render title and children directly in the container for testing
    return React.createElement('div', { 'data-testid': 'head-mock' }, [
      title && React.createElement('title', { key: 'title' }, title),
      children
    ].filter(Boolean));
  },
}));

describe('SeoHead Component', () => {
  const mockMeta = {
    title: 'Test Page Title',
    description: 'Test page description',
    keywords: 'test, seo, meta',
    canonical: 'https://example.com/test',
    og_title: 'Test OG Title',
    og_description: 'Test OG Description',
    og_image: 'https://example.com/image.jpg',
    og_type: 'website',
    twitter_card: 'summary_large_image',
    twitter_title: 'Test Twitter Title',
    twitter_description: 'Test Twitter Description',
    robots: 'index, follow',
  };

  const mockStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'ConvertOKit',
    description: 'Meta advertising and web analytics specialists',
  };

  const mockBreadcrumbs = [
    { name: 'Home', url: 'https://example.com/' },
    { name: 'Services', url: 'https://example.com/services' },
    { name: 'Meta Ads', url: 'https://example.com/services/meta-ads' },
  ];

  it('renders basic meta tags correctly', () => {
    // Test that the component renders without throwing
    const { container } = render(<SeoHead meta={mockMeta} />);
    expect(container).toBeInTheDocument();

    // Test that the Head mock is present
    const headMock = container.querySelector('[data-testid="head-mock"]');
    expect(headMock).toBeInTheDocument();
  });

  it('renders Open Graph meta tags correctly', () => {
    const { container } = render(<SeoHead meta={mockMeta} />);

    // Test that the component renders without throwing
    const headMock = container.querySelector('[data-testid="head-mock"]');
    expect(headMock).toBeInTheDocument();
  });

  it('renders Twitter Card meta tags correctly', () => {
    const { container } = render(<SeoHead meta={mockMeta} />);

    const headMock = container.querySelector('[data-testid="head-mock"]');
    expect(headMock).toBeInTheDocument();
  });

  it('renders robots meta tag correctly', () => {
    const { container } = render(<SeoHead meta={mockMeta} />);

    const headMock = container.querySelector('[data-testid="head-mock"]');
    expect(headMock).toBeInTheDocument();
  });

  it('renders structured data correctly', () => {
    const { container } = render(
      <SeoHead meta={mockMeta} structuredData={mockStructuredData} />
    );

    const structuredDataScript = container.querySelector(
      'script[type="application/ld+json"]'
    );
    expect(structuredDataScript).toBeInTheDocument();
    expect(structuredDataScript?.textContent).toBe(
      JSON.stringify(mockStructuredData)
    );
  });

  it('renders breadcrumb structured data correctly', () => {
    const { container } = render(
      <SeoHead meta={mockMeta} breadcrumbs={mockBreadcrumbs} />
    );

    const scripts = container.querySelectorAll('script[type="application/ld+json"]');
    expect(scripts).toHaveLength(1);

    const breadcrumbScript = Array.from(scripts).find(script => {
      const content = JSON.parse(script.textContent || '{}');
      return content['@type'] === 'BreadcrumbList';
    });

    expect(breadcrumbScript).toBeInTheDocument();
    
    const breadcrumbData = JSON.parse(breadcrumbScript?.textContent || '{}');
    expect(breadcrumbData['@context']).toBe('https://schema.org');
    expect(breadcrumbData['@type']).toBe('BreadcrumbList');
    expect(breadcrumbData.itemListElement).toHaveLength(3);
    expect(breadcrumbData.itemListElement[0].name).toBe('Home');
    expect(breadcrumbData.itemListElement[0].item).toBe('https://example.com/');
  });

  it('renders both structured data and breadcrumbs correctly', () => {
    const { container } = render(
      <SeoHead
        meta={mockMeta}
        structuredData={mockStructuredData}
        breadcrumbs={mockBreadcrumbs}
      />
    );

    const scripts = container.querySelectorAll('script[type="application/ld+json"]');
    expect(scripts).toHaveLength(2);

    const organizationScript = Array.from(scripts).find(script => {
      const content = JSON.parse(script.textContent || '{}');
      return content['@type'] === 'Organization';
    });

    const breadcrumbScript = Array.from(scripts).find(script => {
      const content = JSON.parse(script.textContent || '{}');
      return content['@type'] === 'BreadcrumbList';
    });

    expect(organizationScript).toBeInTheDocument();
    expect(breadcrumbScript).toBeInTheDocument();
  });

  it('handles missing optional props gracefully', () => {
    const minimalMeta = {
      title: 'Minimal Title',
      description: 'Minimal description',
      canonical: 'https://example.com/minimal',
    };

    const { container } = render(<SeoHead meta={minimalMeta} />);

    const headMock = container.querySelector('[data-testid="head-mock"]');
    expect(headMock).toBeInTheDocument();

    // Should not render structured data when not provided
    expect(container.querySelector('script[type="application/ld+json"]')).toBeNull();
  });

  it('uses fallback values for Open Graph when specific values are not provided', () => {
    const metaWithoutOG = {
      title: 'Test Title',
      description: 'Test Description',
      canonical: 'https://example.com/test',
    };

    const { container } = render(<SeoHead meta={metaWithoutOG} />);

    const headMock = container.querySelector('[data-testid="head-mock"]');
    expect(headMock).toBeInTheDocument();
  });

  it('uses fallback values for Twitter Card when specific values are not provided', () => {
    const metaWithoutTwitter = {
      title: 'Test Title',
      description: 'Test Description',
      canonical: 'https://example.com/test',
    };

    const { container } = render(<SeoHead meta={metaWithoutTwitter} />);

    const headMock = container.querySelector('[data-testid="head-mock"]');
    expect(headMock).toBeInTheDocument();
  });

  it('renders viewport and charset meta tags', () => {
    const { container } = render(<SeoHead meta={mockMeta} />);

    const headMock = container.querySelector('[data-testid="head-mock"]');
    expect(headMock).toBeInTheDocument();
  });
});
